import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../date_range/date_range_themes.dart';

/// Central controller for managing all filter state across the application
///
/// This controller follows the established pattern from DateRangeFilterWidget
/// and provides a unified API for managing:
/// - Date range filters
/// - Sort configuration
/// - Search queries
/// - Custom filter values
///
/// Usage Example:
/// ```dart
/// final controller = FilterController();
/// controller.setDateRange(startDate, endDate);
/// controller.setSort('name');
/// controller.updateFilter('gender', 'Male');
/// ```
class FilterController extends ChangeNotifier {
  // Theme for consistent styling across all filter components
  DateRangeTheme? _theme;

  /// Get the theme, with a fallback to weight theme if null (for hot reload compatibility)
  DateRangeTheme get theme {
    _theme ??= DateRangeTheme.weight; // Initialize if null
    return _theme!;
  }

  // Date range state
  DateTime? _startDate;
  DateTime? _endDate;

  // Sort state
  String? _sortBy;
  bool _isAscending = true;

  // Search state
  String _searchQuery = '';

  // Custom filter state
  final Map<String, dynamic> _activeFilterValues = {};

  /// Create a FilterController with the specified theme
  FilterController({DateRangeTheme? theme}) : _theme = theme;

  /// Create a FilterController with default theme (for backward compatibility)
  FilterController.withDefaultTheme() : _theme = DateRangeTheme.weight;

  // Getters for current state
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get sortBy => _sortBy;
  bool get isAscending => _isAscending;
  String get searchQuery => _searchQuery;
  Map<String, dynamic> get activeFilterValues => Map.unmodifiable(_activeFilterValues);

  /// Set the date range filter
  void setDateRange(DateTime? start, DateTime? end) {
    if (_startDate != start || _endDate != end) {
      _startDate = start;
      _endDate = end;
      notifyListeners();
    }
  }

  /// Set the sort field
  void setSort(String? newSortBy) {
    if (_sortBy != newSortBy) {
      _sortBy = newSortBy;
      notifyListeners();
    }
  }

  /// Toggle sort direction
  void toggleSortDirection() {
    _isAscending = !_isAscending;
    notifyListeners();
  }

  /// Update a specific filter value with dependency handling
  void updateFilter(String filterKey, dynamic value, {List<String>? clearDependents}) {
    if (value == null || value == '' || (value is List && value.isEmpty)) {
      // Remove filter if value is null, empty string, or empty list
      if (_activeFilterValues.containsKey(filterKey)) {
        _activeFilterValues.remove(filterKey);
        _clearDependentFilters(filterKey, clearDependents);
        notifyListeners();
      }
    } else {
      // Set or update filter value
      if (_activeFilterValues[filterKey] != value) {
        _activeFilterValues[filterKey] = value;
        _clearDependentFilters(filterKey, clearDependents);
        notifyListeners();
      }
    }
  }

  /// Clear filters that depend on the changed filter
  void _clearDependentFilters(String changedFilterKey, List<String>? dependents) {
    if (dependents != null) {
      for (final dependent in dependents) {
        if (_activeFilterValues.containsKey(dependent)) {
          _activeFilterValues.remove(dependent);
        }
      }
    }
  }

  /// Get dependency values for a specific filter
  Map<String, dynamic>? getDependencyValues(List<String>? dependencies) {
    if (dependencies == null || dependencies.isEmpty) {
      return null;
    }

    final dependencyValues = <String, dynamic>{};
    for (final dependency in dependencies) {
      if (_activeFilterValues.containsKey(dependency)) {
        dependencyValues[dependency] = _activeFilterValues[dependency];
      }
    }

    return dependencyValues.isEmpty ? null : dependencyValues;
  }

  /// Check if dependencies are satisfied for a filter
  bool areDependenciesSatisfied(List<String>? dependencies) {
    if (dependencies == null || dependencies.isEmpty) {
      return true; // No dependencies means always satisfied
    }

    return dependencies.every((dependency) => _activeFilterValues.containsKey(dependency));
  }

  /// Set the search query
  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      notifyListeners();
    }
  }

  /// Clear all filters and reset to default state
  void clearAll() {
    bool hasChanges = false;

    if (_startDate != null || _endDate != null) {
      _startDate = null;
      _endDate = null;
      hasChanges = true;
    }

    if (_sortBy != null) {
      _sortBy = null;
      hasChanges = true;
    }

    if (!_isAscending) {
      _isAscending = true;
      hasChanges = true;
    }

    if (_searchQuery.isNotEmpty) {
      _searchQuery = '';
      hasChanges = true;
    }

    if (_activeFilterValues.isNotEmpty) {
      _activeFilterValues.clear();
      hasChanges = true;
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// Check if any filters are currently active
  bool get hasActiveFilters {
    return _startDate != null ||
           _endDate != null ||
           _sortBy != null ||
           _searchQuery.isNotEmpty ||
           _activeFilterValues.isNotEmpty;
  }

  /// Get human-readable labels for all active filters
  List<String> get activeFilterLabels {
    final labels = <String>[];

    // Add date range label
    if (_startDate != null && _endDate != null) {
      labels.add(_getDateRangeLabel());
    }

    // Add sort label (show only the sort field and direction)
    if (_sortBy != null) {
      final direction = _isAscending ? '↑' : '↓';
      labels.add('$_sortBy $direction');
    }

    // Add search label (show only the search term)
    if (_searchQuery.isNotEmpty) {
      labels.add('"$_searchQuery"');
    }

    // Add custom filter labels (show only values, not field names)
    _activeFilterValues.forEach((key, value) {
      if (value is List) {
        if (value.isNotEmpty) {
          // Add each value as a separate chip
          for (final item in value) {
            labels.add(item.toString());
          }
        }
      } else {
        labels.add(value.toString());
      }
    });

    return labels;
  }

  /// Get a formatted label for the current date range
  String _getDateRangeLabel() {
    if (_startDate == null || _endDate == null) return '';

    // Check for common presets (reusing logic from DateRangeFilterWidget)
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Check if it's today
    if (_isSameDay(_startDate!, today) && _isSameDay(_endDate!, today)) {
      return 'Today';
    }
    
    // Check if it's yesterday
    final yesterday = today.subtract(const Duration(days: 1));
    if (_isSameDay(_startDate!, yesterday) && _isSameDay(_endDate!, yesterday)) {
      return 'Yesterday';
    }
    
    // Check for "Last X Days" patterns
    final daysDiff = today.difference(_startDate!).inDays;
    if (_isSameDay(_endDate!, today)) {
      switch (daysDiff) {
        case 7:
          return 'Last 7 Days';
        case 30:
          return 'Last 30 Days';
        case 90:
          return 'Last 90 Days';
        case 180:
          return 'Last 6 Months';
      }
    }
    
    // Custom date range - format the dates
    final formatter = DateFormat('MMM dd, yyyy');
    return '${formatter.format(_startDate!)} - ${formatter.format(_endDate!)}';
  }

  /// Helper method to check if two dates are on the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
