import 'package:uuid/uuid.dart';
import 'package:isar/isar.dart';
import '../../../services/database/database_helper.dart';
import '../models/weight_record_isar.dart';

class WeightService {
  static final WeightService _instance = WeightService._internal();
  factory WeightService() => _instance;
  WeightService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final Uuid _uuid = const Uuid();

  // CRUD Operations
  Future<String> addWeightRecord(WeightRecordIsar record) async {
    try {
      final businessId = _uuid.v4();
      record.businessId = businessId;
      record.createdAt = DateTime.now();
      record.updatedAt = DateTime.now();
      
      // Calculate growth data if previous weight exists
      await _calculateGrowthData(record);
      
      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        await isar.weightRecordIsars.put(record);
      });
      
      return businessId;
    } catch (e) {
      throw Exception('Failed to add weight record: $e');
    }
  }

  Future<void> updateWeightRecord(WeightRecordIsar record) async {
    try {
      record.updatedAt = DateTime.now();
      
      // Recalculate growth data
      await _calculateGrowthData(record);
      
      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        await isar.weightRecordIsars.put(record);
      });
    } catch (e) {
      throw Exception('Failed to update weight record: $e');
    }
  }

  Future<void> deleteWeightRecord(String businessId) async {
    try {
      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        final record = await isar.weightRecordIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();
        if (record != null) {
          await isar.weightRecordIsars.delete(record.id);
        }
      });
    } catch (e) {
      throw Exception('Failed to delete weight record: $e');
    }
  }

  Future<WeightRecordIsar?> getWeightRecord(String businessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      throw Exception('Failed to get weight record: $e');
    }
  }

  Future<List<WeightRecordIsar>> getAllWeightRecords() async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .where()
          .sortByMeasurementDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get weight records: $e');
    }
  }

  Future<List<WeightRecordIsar>> getWeightRecordsByCattle(String cattleBusinessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByMeasurementDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get cattle weight records: $e');
    }
  }

  Future<List<WeightRecordIsar>> getWeightRecordsByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .filter()
          .measurementDateBetween(startDate, endDate)
          .sortByMeasurementDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get weight records by date range: $e');
    }
  }

  // Analytics Methods
  Future<WeightRecordIsar?> getLatestWeightRecord(String cattleBusinessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByMeasurementDateDesc()
          .findFirst();
    } catch (e) {
      throw Exception('Failed to get latest weight record: $e');
    }
  }

  Future<double> getAverageWeight(String cattleBusinessId, {int? lastNRecords}) async {
    try {
      final isar = await _dbHelper.database;
      var records = await isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByMeasurementDateDesc()
          .findAll();

      // Apply limit manually if specified
      if (lastNRecords != null && records.length > lastNRecords) {
        records = records.take(lastNRecords).toList();
      }

      if (records.isEmpty) return 0.0;

      final totalWeight = records.fold<double>(0.0, (sum, record) => sum + record.weight);
      return totalWeight / records.length;
    } catch (e) {
      throw Exception('Failed to calculate average weight: $e');
    }
  }

  Future<double> getTotalWeightGain(String cattleBusinessId) async {
    try {
      final records = await getWeightRecordsByCattle(cattleBusinessId);
      if (records.length < 2) return 0.0;
      
      final firstRecord = records.last; // Oldest record
      final lastRecord = records.first; // Latest record
      
      return lastRecord.weight - firstRecord.weight;
    } catch (e) {
      throw Exception('Failed to calculate total weight gain: $e');
    }
  }

  Future<double> getAverageDailyGain(String cattleBusinessId, {int? days}) async {
    try {
      final records = await getWeightRecordsByCattle(cattleBusinessId);
      if (records.length < 2) return 0.0;
      
      final endDate = DateTime.now();
      final startDate = days != null 
          ? endDate.subtract(Duration(days: days))
          : records.last.measurementDate ?? endDate;
      
      final filteredRecords = records.where((record) {
        final date = record.measurementDate ?? DateTime.now();
        return date.isAfter(startDate) && date.isBefore(endDate);
      }).toList();
      
      if (filteredRecords.length < 2) return 0.0;
      
      final totalGain = filteredRecords.first.weight - filteredRecords.last.weight;
      final totalDays = filteredRecords.first.measurementDate!
          .difference(filteredRecords.last.measurementDate!)
          .inDays;
      
      return totalDays > 0 ? totalGain / totalDays : 0.0;
    } catch (e) {
      throw Exception('Failed to calculate average daily gain: $e');
    }
  }

  // Growth Analysis
  Future<Map<String, dynamic>> getGrowthAnalysis(String cattleBusinessId) async {
    try {
      final records = await getWeightRecordsByCattle(cattleBusinessId);
      if (records.isEmpty) {
        return {
          'totalRecords': 0,
          'currentWeight': 0.0,
          'totalGain': 0.0,
          'averageDailyGain': 0.0,
          'growthTrend': 'No data',
          'lastMeasurement': null,
        };
      }

      final currentWeight = records.first.weight;
      final totalGain = await getTotalWeightGain(cattleBusinessId);
      final avgDailyGain = await getAverageDailyGain(cattleBusinessId);
      
      // Determine growth trend
      String growthTrend = 'Stable';
      if (records.length >= 3) {
        final recent = records.take(3).toList();
        final weights = recent.map((r) => r.weight).toList();
        
        if (weights[0] > weights[1] && weights[1] > weights[2]) {
          growthTrend = 'Increasing';
        } else if (weights[0] < weights[1] && weights[1] < weights[2]) {
          growthTrend = 'Decreasing';
        } else {
          growthTrend = 'Variable';
        }
      }

      return {
        'totalRecords': records.length,
        'currentWeight': currentWeight,
        'totalGain': totalGain,
        'averageDailyGain': avgDailyGain,
        'growthTrend': growthTrend,
        'lastMeasurement': records.first.measurementDate,
        'firstMeasurement': records.last.measurementDate,
        'averageWeight': await getAverageWeight(cattleBusinessId),
      };
    } catch (e) {
      throw Exception('Failed to get growth analysis: $e');
    }
  }

  // Helper Methods
  Future<void> _calculateGrowthData(WeightRecordIsar record) async {
    try {
      if (record.cattleBusinessId == null) return;
      
      final previousRecord = await _getPreviousWeightRecord(
        record.cattleBusinessId!, 
        record.measurementDate ?? DateTime.now()
      );
      
      if (previousRecord != null) {
        record.previousWeight = previousRecord.weight;
        record.weightGain = record.weight - previousRecord.weight;
        
        final daysDiff = (record.measurementDate ?? DateTime.now())
            .difference(previousRecord.measurementDate ?? DateTime.now())
            .inDays;
        
        record.daysSinceLastMeasurement = daysDiff;
        record.dailyGain = daysDiff > 0 ? record.weightGain! / daysDiff : 0.0;
      }
    } catch (e) {
      // Log error but don't throw to avoid breaking the main operation
      // Error calculating growth data: $e
    }
  }

  Future<WeightRecordIsar?> _getPreviousWeightRecord(
    String cattleBusinessId,
    DateTime currentDate
  ) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .measurementDateLessThan(currentDate)
          .sortByMeasurementDateDesc()
          .findFirst();
    } catch (e) {
      return null;
    }
  }

  // Weight Goals
  Future<String> addWeightGoal(WeightGoalIsar goal) async {
    try {
      final businessId = _uuid.v4();
      goal.businessId = businessId;
      goal.createdAt = DateTime.now();
      goal.updatedAt = DateTime.now();
      
      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        await isar.weightGoalIsars.put(goal);
      });
      
      return businessId;
    } catch (e) {
      throw Exception('Failed to add weight goal: $e');
    }
  }

  Future<List<WeightGoalIsar>> getActiveWeightGoals(String cattleBusinessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.weightGoalIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .statusEqualTo('active')
          .findAll();
    } catch (e) {
      throw Exception('Failed to get active weight goals: $e');
    }
  }
}
