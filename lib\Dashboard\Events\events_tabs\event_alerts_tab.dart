import 'package:flutter/material.dart';
import '../models/event_isar.dart';
import '../../../services/database/database_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EventAlertsTab extends StatefulWidget {
  const EventAlertsTab({Key? key}) : super(key: key);

  @override
  State<EventAlertsTab> createState() => _EventAlertsTabState();
}

class _EventAlertsTabState extends State<EventAlertsTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<EventIsar> _events = [];
  bool _isLoading = true;
  bool _showNotificationSettings = false;

  // Notification preferences
  bool _enableUpcomingReminders = true;
  bool _enableMissedAlerts = true;
  bool _enableCustomAlerts = true;
  int _upcomingReminderDays = 7;
  int _missedAlertHours = 24;

  @override
  void initState() {
    super.initState();
    _loadEvents();
    _loadNotificationPreferences();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() => _isLoading = true);

      final allEvents = await _dbHelper.eventsHandler.getAllEvents();

      // Load events based on notification settings
      final now = DateTime.now();
      final missedEvents = _enableMissedAlerts
          ? allEvents.where((event) => 
              event.eventDate != null && 
              event.eventDate!.isBefore(now) && 
              !event.isCompleted).toList()
          : [];
          
      final importantEvents = _enableCustomAlerts
          ? allEvents.where((event) => 
              event.priority == EventPriority.high && 
              !event.isCompleted).toList()
          : [];
          
      final upcomingEvents = _enableUpcomingReminders
          ? allEvents.where((event) => 
              event.eventDate != null && 
              event.eventDate!.isAfter(now) && 
              event.eventDate!.isBefore(now.add(Duration(days: _upcomingReminderDays))) && 
              !event.isCompleted).toList()
          : [];

      if (mounted) {
        setState(() {
          _events = [...missedEvents, ...importantEvents, ...upcomingEvents];
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading events: $e');
      if (mounted) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _enableUpcomingReminders =
          prefs.getBool('enable_upcoming_reminders') ?? true;
      _enableMissedAlerts = prefs.getBool('enable_missed_alerts') ?? true;
      _enableCustomAlerts = prefs.getBool('enable_custom_alerts') ?? true;
      _upcomingReminderDays = prefs.getInt('upcoming_reminder_days') ?? 7;
      _missedAlertHours = prefs.getInt('missed_alert_hours') ?? 24;
    });
  }

  Future<void> _saveNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('enable_upcoming_reminders', _enableUpcomingReminders);
    await prefs.setBool('enable_missed_alerts', _enableMissedAlerts);
    await prefs.setBool('enable_custom_alerts', _enableCustomAlerts);
    await prefs.setInt('upcoming_reminder_days', _upcomingReminderDays);
    await prefs.setInt('missed_alert_hours', _missedAlertHours);
  }

  Future<void> _markAsComplete(EventIsar event) async {
    try {
      if (event.businessId != null) {
        await _dbHelper.eventsHandler.markEventAsCompleted(event.businessId!, true);
        _loadEvents();
      }
    } catch (e) {
      debugPrint('Error marking event as complete: $e');
    }
  }

  Widget _buildNotificationSettings() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Notification Settings',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () =>
                      setState(() => _showNotificationSettings = false),
                ),
              ],
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Upcoming Event Reminders'),
              subtitle: const Text('Get notified about upcoming events'),
              value: _enableUpcomingReminders,
              onChanged: (value) => setState(() {
                _enableUpcomingReminders = value;
                _saveNotificationPreferences();
              }),
            ),
            if (_enableUpcomingReminders)
              ListTile(
                title: const Text('Reminder Days Before'),
                trailing: DropdownButton<int>(
                  value: _upcomingReminderDays,
                  items: [1, 3, 5, 7, 14].map((days) {
                    return DropdownMenuItem(
                      value: days,
                      child: Text('$days days'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _upcomingReminderDays = value;
                        _saveNotificationPreferences();
                      });
                    }
                  },
                ),
              ),
            SwitchListTile(
              title: const Text('Missed Event Alerts'),
              subtitle: const Text('Get notified about missed events'),
              value: _enableMissedAlerts,
              onChanged: (value) => setState(() {
                _enableMissedAlerts = value;
                _saveNotificationPreferences();
              }),
            ),
            if (_enableMissedAlerts)
              ListTile(
                title: const Text('Alert After Hours'),
                trailing: DropdownButton<int>(
                  value: _missedAlertHours,
                  items: [6, 12, 24, 48].map((hours) {
                    return DropdownMenuItem(
                      value: hours,
                      child: Text('$hours hours'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _missedAlertHours = value;
                        _saveNotificationPreferences();
                      });
                    }
                  },
                ),
              ),
            SwitchListTile(
              title: const Text('Priority Based Alerts'),
              subtitle: const Text('Get special alerts for high priority events'),
              value: _enableCustomAlerts,
              onChanged: (value) => setState(() {
                _enableCustomAlerts = value;
                _saveNotificationPreferences();
              }),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _saveNotificationPreferences();
                  _loadEvents();
                  setState(() => _showNotificationSettings = false);
                },
                child: const Text('Apply Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getEventStatusText(EventIsar event) {
    final now = DateTime.now();
    
    if (event.isCompleted) {
      return 'Completed';
    }
    
    if (event.eventDate != null) {
      if (event.eventDate!.isBefore(now)) {
        final difference = now.difference(event.eventDate!);
        if (difference.inDays > 0) {
          return 'Overdue by ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
        } else if (difference.inHours > 0) {
          return 'Overdue by ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
        } else {
          return 'Overdue by ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
        }
      } else {
        final difference = event.eventDate!.difference(now);
        if (difference.inDays > 0) {
          return 'Due in ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
        } else if (difference.inHours > 0) {
          return 'Due in ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
        } else {
          return 'Due in ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
        }
      }
    }
    
    return 'No due date';
  }

  Color _getStatusColor(EventIsar event) {
    if (event.isCompleted) {
      return Colors.green;
    }
    
    if (event.eventDate != null) {
      if (event.eventDate!.isBefore(DateTime.now())) {
        return Colors.red;
      } else {
        final difference = event.eventDate!.difference(DateTime.now());
        if (difference.inDays <= 1) {
          return Colors.orange;
        }
      }
    }
    
    if (event.priority == EventPriority.high) {
      return Colors.purple;
    }
    
    return Colors.blue;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_showNotificationSettings) {
      return _buildNotificationSettings();
    }

    return Column(
      children: [
        // Settings button
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Alert Settings',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              ElevatedButton.icon(
                onPressed: () => setState(() => _showNotificationSettings = true),
                icon: const Icon(Icons.settings),
                label: const Text('Configure'),
              ),
            ],
          ),
        ),
        
        // Events list
        Expanded(
          child: _events.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_off,
                        size: 64,
                        color: Theme.of(context).primaryColor.withAlpha(128),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No alerts at this time',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'All upcoming events are on track',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _events.length,
                  itemBuilder: (context, index) {
                    final event = _events[index];
                    final statusText = _getEventStatusText(event);
                    final statusColor = _getStatusColor(event);
                    final icon = event.type?.getIcon() ?? Icons.event;
                    final typeColor = event.type?.getColor() ?? Colors.grey;
                    
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: typeColor.withAlpha(40),
                          child: Icon(icon, color: typeColor),
                        ),
                        title: Text(
                          event.title ?? 'Untitled Event',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            decoration: event.isCompleted ? TextDecoration.lineThrough : null,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (event.type != null)
                              Text(event.type!.getDisplayName()),
                            if (event.eventDate != null)
                              Text(
                                '${event.eventDate!.toLocal().toString().split(' ')[0]} ${event.time?.toTimeOfDay().format(context) ?? ''}',
                              ),
                            Text(
                              statusText,
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        trailing: IconButton(
                          icon: Icon(
                            event.isCompleted
                                ? Icons.check_circle
                                : Icons.check_circle_outline,
                            color: event.isCompleted ? Colors.green : Colors.grey,
                          ),
                          onPressed: () => _markAsComplete(event),
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
}
