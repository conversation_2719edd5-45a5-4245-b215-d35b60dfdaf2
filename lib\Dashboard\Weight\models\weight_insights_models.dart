/// Type-safe data models for weight insights
/// Replaces Map<String, dynamic> usage for compile-time safety

/// Analytics summary data for the analytics tab
class AnalyticsSummary {
  final int totalRecords;
  final int totalCattle;
  final double averageWeight;
  final double totalWeight;
  final DateTime? earliestRecord;
  final DateTime? latestRecord;

  const AnalyticsSummary({
    required this.totalRecords,
    required this.totalCattle,
    required this.averageWeight,
    required this.totalWeight,
    this.earliestRecord,
    this.latestRecord,
  });

  static const AnalyticsSummary empty = AnalyticsSummary(
    totalRecords: 0,
    totalCattle: 0,
    averageWeight: 0.0,
    totalWeight: 0.0,
  );
}

/// Performance insights data
class PerformanceInsights {
  final String topPerformerName;
  final String topPerformerId;
  final double topPerformerGainRate;
  final double averageDailyGain;
  final String growthTrend;
  final String growthTrendDescription;
  final int totalGainingCattle;
  final int totalLosingCattle;

  const PerformanceInsights({
    required this.topPerformerName,
    required this.topPerformerId,
    required this.topPerformerGainRate,
    required this.averageDailyGain,
    required this.growthTrend,
    required this.growthTrendDescription,
    required this.totalGainingCattle,
    required this.totalLosingCattle,
  });

  static const PerformanceInsights empty = PerformanceInsights(
    topPerformerName: 'No data',
    topPerformerId: '',
    topPerformerGainRate: 0.0,
    averageDailyGain: 0.0,
    growthTrend: 'Stable',
    growthTrendDescription: 'No trend data available',
    totalGainingCattle: 0,
    totalLosingCattle: 0,
  );
}

/// Health insights data
class HealthInsights {
  final double averageBodyCondition;
  final String consistencyRating;
  final String consistencyDescription;
  final int healthAlerts;
  final List<String> alertReasons;
  final int underweightCattle;
  final int overweightCattle;
  final int normalWeightCattle;

  const HealthInsights({
    required this.averageBodyCondition,
    required this.consistencyRating,
    required this.consistencyDescription,
    required this.healthAlerts,
    required this.alertReasons,
    required this.underweightCattle,
    required this.overweightCattle,
    required this.normalWeightCattle,
  });

  static const HealthInsights empty = HealthInsights(
    averageBodyCondition: 0.0,
    consistencyRating: 'Good',
    consistencyDescription: 'No data available',
    healthAlerts: 0,
    alertReasons: [],
    underweightCattle: 0,
    overweightCattle: 0,
    normalWeightCattle: 0,
  );
}

/// Trend insights data
class TrendInsights {
  final String overallTrend;
  final String trendDescription;
  final double trendPercentage;
  final String seasonalPattern;
  final String seasonalDescription;
  final List<MonthlyTrend> monthlyTrends;
  final String recommendation;

  const TrendInsights({
    required this.overallTrend,
    required this.trendDescription,
    required this.trendPercentage,
    required this.seasonalPattern,
    required this.seasonalDescription,
    required this.monthlyTrends,
    required this.recommendation,
  });

  static const TrendInsights empty = TrendInsights(
    overallTrend: 'Stable',
    trendDescription: 'No trend data available',
    trendPercentage: 0.0,
    seasonalPattern: 'No pattern',
    seasonalDescription: 'Insufficient data',
    monthlyTrends: [],
    recommendation: 'Continue monitoring',
  );
}

/// Monthly trend data point
class MonthlyTrend {
  final int month;
  final int year;
  final double averageWeight;
  final int recordCount;
  final double changeFromPrevious;

  const MonthlyTrend({
    required this.month,
    required this.year,
    required this.averageWeight,
    required this.recordCount,
    required this.changeFromPrevious,
  });
}

/// Recommendation data
class WeightRecommendation {
  final String title;
  final String description;
  final String priority; // 'High', 'Medium', 'Low'
  final String category; // 'Nutrition', 'Health', 'Management', etc.
  final List<String> actionItems;

  const WeightRecommendation({
    required this.title,
    required this.description,
    required this.priority,
    required this.category,
    required this.actionItems,
  });
}

/// Complete insights data combining all insight types
class WeightInsightsData {
  final PerformanceInsights performance;
  final HealthInsights health;
  final TrendInsights trends;
  final List<WeightRecommendation> recommendations;
  final DateTime lastUpdated;

  const WeightInsightsData({
    required this.performance,
    required this.health,
    required this.trends,
    required this.recommendations,
    required this.lastUpdated,
  });

  static WeightInsightsData get empty => WeightInsightsData(
    performance: PerformanceInsights.empty,
    health: HealthInsights.empty,
    trends: TrendInsights.empty,
    recommendations: const [],
    lastUpdated: DateTime.now(),
  );
}
