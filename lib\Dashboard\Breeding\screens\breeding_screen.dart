import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../constants/app_colors.dart';
import '../screens/breeding_records_screen.dart';
import '../screens/pregnancy_records_screen.dart';
import '../screens/delivery_records_screen.dart';
import '../screens/heat_calendar_screen.dart';

class BreedingScreen extends StatefulWidget {
  const BreedingScreen({super.key});

  @override
  State<BreedingScreen> createState() => _BreedingScreenState();
}

class _BreedingScreenState extends State<BreedingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Breeding Management',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart, color: Colors.white),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.breedingReport,
            ),
            tooltip: 'View Breeding Reports',
          ),
        ],
      ),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildNavigationCard(
              context,
              'Breeding Records',
              Icons.favorite,
              'View and manage breeding records for all cattle',
              Colors.red,
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BreedingRecordsScreen(),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              'Pregnancy Records',
              Icons.pets,
              'Track and monitor pregnancy status and due dates',
              Colors.purple,
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PregnancyRecordsScreen(),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              'Delivery Records',
              Icons.child_care,
              'Manage delivery records and calf information',
              Colors.blue,
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DeliveryRecordsScreen(),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              'Heat Calendar',
              Icons.calendar_today,
              'Track and monitor heat cycles and breeding events',
              Colors.green,
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HeatCalendarScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationCard(
    BuildContext context,
    String title,
    IconData icon,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(51),
          radius: 24,
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }
}
