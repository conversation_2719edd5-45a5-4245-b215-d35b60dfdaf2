import 'package:flutter/material.dart';
import 'controllers/filter_controller.dart';
import 'date_range/date_range_themes.dart';
import '../../../utils/responsive_helpers.dart';

/// Simplified filter status bar that works with FilterController
///
/// This widget follows the established pattern and provides:
/// - Automatic visibility based on controller state
/// - Display of active filter labels from controller
/// - Clear filters functionality
/// - Record count display
/// - Consistent theming across modules
///
/// Usage Example:
/// ```dart
/// FilterStatusBar(
///   controller: filterController,
///   totalCount: totalRecords,
///   filteredCount: filteredRecords,
/// )
/// ```
class FilterStatusBar extends StatelessWidget {
  final FilterController controller;
  final int? totalCount;
  final int? filteredCount;
  final EdgeInsets? padding;
  final bool showRecordCount;

  const FilterStatusBar({
    Key? key,
    required this.controller,
    this.totalCount,
    this.filteredCount,
    this.padding,
    this.showRecordCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        // Only show when filters are active
        if (!controller.hasActiveFilters) {
          return const SizedBox.shrink();
        }

        final activeLabels = controller.activeFilterLabels;

        return Container(
          margin: padding ?? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Simple row with clear button and active filter info
              Row(
                children: [
                  // Clear Filters Button
                  _buildEnhancedClearButton(context),

                  // Spacing between elements
                  const SizedBox(width: 12),

                  // Active filters display
                  Expanded(
                    child: _buildEnhancedActiveFiltersDisplay(context, activeLabels),
                  ),
                ],
              ),

              // Record count at bottom (if available) - enhanced styling
              if (_shouldShowCount())
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: controller.theme.color.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Showing ${filteredCount ?? 0} of ${totalCount ?? 0} records',
                      style: TextStyle(
                        color: controller.theme.color.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEnhancedActiveFiltersDisplay(BuildContext context, List<String> activeLabels) {
    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: activeLabels.map((label) {
        return Container(
          height: 40, // Fixed height to match clear button
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: controller.theme.color,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: controller.theme.color.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEnhancedClearButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          controller.clearAll();
          // Show enhanced feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text('All filters cleared'),
                ],
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: controller.theme.color,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 40, // Fixed height to match active filter chips
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: controller.theme.color.withValues(alpha: 0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.clear_all,
                size: 18,
                color: controller.theme.color,
              ),
              const SizedBox(width: 8),
              Text(
                'Clear Filters',
                style: TextStyle(
                  color: controller.theme.color,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Keep old methods for backward compatibility
  Widget _buildActiveFiltersDisplay(BuildContext context, List<String> activeLabels) {
    return _buildEnhancedActiveFiltersDisplay(context, activeLabels);
  }

  bool _shouldShowCount() {
    return showRecordCount &&
           totalCount != null &&
           filteredCount != null;
  }

  /// Determine if we should use equal space (50/50) or responsive sizing
  bool _shouldUseEqualSpace(List<String> activeLabels) {
    // Use equal space when active filters text is short
    final totalActiveText = activeLabels.join(', ').length;
    return totalActiveText <= 30; // Threshold for "short" text
  }

  /// Build the clear button widget
  Widget _buildClearButton(BuildContext context) {
    return InkWell(
      onTap: () {
        controller.clearAll();
        // Show feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Filters cleared'),
            duration: const Duration(seconds: 1),
            backgroundColor: controller.theme.color,
          ),
        );
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        height: 44, // Fixed height to match filter chips
        padding: EdgeInsets.symmetric(
          horizontal: ResponsiveHelpers.getPadding(context),
          vertical: ResponsiveHelpers.getVerticalPadding(context),
        ),
        decoration: BoxDecoration(
          color: controller.theme.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: controller.theme.color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.clear,
              size: ResponsiveHelpers.getIconSize(context),
              color: controller.theme.color,
            ),
            SizedBox(width: ResponsiveHelpers.getSpacing(context)),
            Text(
              'Clear Filters',
              style: TextStyle(
                color: controller.theme.color,
                fontSize: ResponsiveHelpers.getFontSize(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }




}
