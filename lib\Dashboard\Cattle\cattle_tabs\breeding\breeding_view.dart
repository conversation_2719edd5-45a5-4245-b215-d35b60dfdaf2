import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import '../../models/cattle_isar.dart';
import 'package:cattle_manager/services/database/database_helper.dart';
import '../../../Breeding/dialogs/breeding_form_dialog.dart';
import '../../widgets/breeding_history_card.dart';
import '../../widgets/eligibility_card.dart';
import '../../widgets/stats_card.dart';
import '../../../Breeding/models/breeding_record_isar.dart';
import '../../../Breeding/models/pregnancy_record_isar.dart';
import '../../../../services/streams/stream_service.dart';
import '../../../../constants/app_colors.dart';
import '../../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';
import 'package:collection/collection.dart';

class BreedingView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;

  const BreedingView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<BreedingView> createState() => _BreedingViewState();
}

class _BreedingViewState extends State<BreedingView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  StreamSubscription<Map<String, dynamic>>? _breedingRecordSubscription;
  StreamSubscription<Map<String, dynamic>>? _pregnancyRecordSubscription;
  bool _isLoading = true;
  bool _isPregnant = false; // Local pregnancy state for immediate UI updates

  // Add timestamp tracking for breeding update
  DateTime? _lastPregnancyStatusUpdateTime;
  final _minimumUpdateInterval = const Duration(seconds: 5);

  // Store breeding records in state
  List<BreedingRecordIsar> _breedingRecords = [];

  // Helper method to safely update state

  // Helper method to convert errors to user-friendly messages
  String _getReadableErrorMessage(dynamic error) {
    final message = error.toString();

    // Check for specific error types
    if (message.contains('ValidationException')) {
      return message.replaceAll('ValidationException: ', '');
    } else if (message.contains('DatabaseException')) {
      return 'A database error occurred. Please try again.';
    } else if (message.contains('Cattle ID is missing')) {
      return 'Cattle ID is missing. Please select a valid cattle.';
    }

    // Generic error message as fallback
    return 'An error occurred. Please check your inputs and try again.';
  }

  @override
  void initState() {
    super.initState();
    debugPrint('🔄 BreedingView initState for ${widget.cattle.tagId}');
    debugPrint('🔄 Initial cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    // Initialize pregnancy state from cattle data
    _isPregnant = widget.cattle.breedingStatus?.isPregnant ?? false;
    _subscribeToRecordUpdates();
    _loadBreedingData();
  }

  @override
  void didUpdateWidget(BreedingView oldWidget) {
    super.didUpdateWidget(oldWidget);
    debugPrint('🔄 BreedingView didUpdateWidget for ${widget.cattle.tagId}');
    debugPrint('🔄 Old cattle lastCalvingDate: ${oldWidget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🔄 New cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');

    // Reload data if cattle changed
    if (oldWidget.cattle.businessId != widget.cattle.businessId ||
        oldWidget.cattle.breedingStatus?.lastCalvingDate != widget.cattle.breedingStatus?.lastCalvingDate) {
      debugPrint('🔄 Cattle data changed, reloading breeding records');
      _isPregnant = widget.cattle.breedingStatus?.isPregnant ?? false;
      _loadBreedingData();
    }
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _pregnancyRecordSubscription?.cancel();
    super.dispose();
  }



  void _subscribeToRecordUpdates() {
    // Cancel existing subscriptions
    _breedingRecordSubscription?.cancel();
    _pregnancyRecordSubscription?.cancel();

    // Get the stream service from GetIt
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to breeding records
    _breedingRecordSubscription =
        streamService.breedingStream.listen((record) async {
      if (mounted) {
        // Get the event details
        final eventCattleId = record['cattleId'] as String?;
        final cattleId = widget.cattle.tagId;
        final action = record['action'] as String?;

        // Check if this event is relevant to our cattle and both IDs are not null
        if (eventCattleId != null &&
            cattleId != null &&
            eventCattleId == cattleId) {
          setState(() => _isLoading = true);

          try {
            // Handle delete action
            if (action == 'delete') {
              // Check both 'id' and 'recordId' fields for compatibility
              final recordId = record['id'] as String? ?? record['recordId'] as String?;

              if (recordId != null && mounted) {
                debugPrint('Removing breeding record with ID: $recordId');

                setState(() {
                  // Remove the record from the list
                  final initialCount = _breedingRecords.length;
                  _breedingRecords.removeWhere((r) => r.businessId == recordId);
                  final finalCount = _breedingRecords.length;

                  debugPrint('Removed ${initialCount - finalCount} breeding records');
                  _isLoading = false;
                });

                // Force rebuild of the history card
                if (mounted) {
                  setState(() {});
                }

                // Fetch the latest cattle data
                final updatedCattle = await _databaseHelper.cattleHandler
                    .getCattleByTagId(cattleId);

                if (updatedCattle != null && mounted) {
                  // Update the cattle data
                  widget.onCattleUpdated(updatedCattle);
                  // Update local pregnancy state immediately
                  setState(() {
                    _isPregnant = updatedCattle.breedingStatus?.isPregnant ?? false;
                  });
                }

                // Check pregnancy status consistency
                await _checkAndUpdatePregnancyStatus();
                return;
              }
            }

            // Handle add/update actions
            final recordId = record['id'] as String?;
            if (recordId != null) {
              // Fetch just the updated record
              final updatedRecord = await _databaseHelper.breedingHandler
                  .getBreedingRecordById(recordId);

              // Fetch the latest cattle data
              final updatedCattle = await _databaseHelper.cattleHandler
                  .getCattleByTagId(cattleId);

              if (updatedCattle != null && mounted) {
                // Update the cattle data
                widget.onCattleUpdated(updatedCattle);
                // Update local pregnancy state immediately
                setState(() {
                  _isPregnant = updatedCattle.breedingStatus?.isPregnant ?? false;
                });
              }

              if (updatedRecord != null && mounted) {
                setState(() {
                  // Find and update the record in the list
                  final index = _breedingRecords.indexWhere(
                      (r) => r.businessId == recordId);
                  if (index >= 0) {
                    _breedingRecords[index] = updatedRecord;
                  } else {
                    // Add new record if not found
                    _breedingRecords.add(updatedRecord);
                    // Sort records
                    _breedingRecords.sort((a, b) => (b.date ?? DateTime(0))
                        .compareTo(a.date ?? DateTime(0)));
                  }
                  _isLoading = false;
                });
              }

              // Check pregnancy status consistency directly
              await _checkAndUpdatePregnancyStatus();
            }
          } catch (e) {
            debugPrint('Error updating breeding view: $e');
            if (mounted) {
              setState(() => _isLoading = false);
            }
          }
        }
      }
    }, onError: (error) {
      debugPrint('Error in breeding record stream: $error');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });

    // Subscribe to pregnancy records
    _pregnancyRecordSubscription =
        streamService.pregnancyStream.listen((record) async {
      if (mounted) {
        // Get the event details
        final eventCattleId = record['cattleId'] as String?;
        final cattleId = widget.cattle.tagId;

        // Check if this event is relevant to our cattle and both IDs are not null
        if (eventCattleId != null &&
            cattleId != null &&
            eventCattleId == cattleId) {
          setState(() => _isLoading = true);

          try {
            // Fetch the latest cattle data
            final updatedCattle =
                await _databaseHelper.cattleHandler.getCattleByTagId(cattleId);

            if (updatedCattle != null && mounted) {
              // Update the cattle data
              widget.onCattleUpdated(updatedCattle);

              // Update local pregnancy state immediately
              setState(() {
                _isPregnant = updatedCattle.breedingStatus?.isPregnant ?? false;
              });

              // Get the breeding record ID associated with this pregnancy
              final breedingRecordId = record['breedingRecordId'] as String?;

              if (breedingRecordId != null) {
                // Fetch the associated breeding record
                final breedingRecord = await _databaseHelper.breedingHandler
                    .getBreedingRecordById(breedingRecordId);

                if (breedingRecord != null && mounted) {
                  setState(() {
                    // Find and update the record in the list
                    final index = _breedingRecords.indexWhere(
                        (r) => r.id.toString() == breedingRecordId.toString());
                    if (index >= 0) {
                      _breedingRecords[index] = breedingRecord;
                    }
                    _isLoading = false;
                  });
                }
              } else {
                // If no breeding record ID, just update the loading state
                if (mounted) {
                  setState(() => _isLoading = false);
                }
              }

              // Check pregnancy status consistency directly
              await _checkAndUpdatePregnancyStatus();
            }
          } catch (e) {
            debugPrint('Error updating breeding view from pregnancy event: $e');
            if (mounted) {
              setState(() => _isLoading = false);
            }
          }
        }
      }
    }, onError: (error) {
      debugPrint('Error in pregnancy record stream: $error');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  // Method to load breeding data and refresh eligibility status
  Future<void> _loadBreedingData() async {
    if (!mounted) return;

    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      debugPrint('Cannot load breeding data: Cattle ID is missing');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Fetch the latest cattle data to ensure we have the most up-to-date information
      final updatedCattle =
          await _databaseHelper.cattleHandler.getCattleByTagId(cattleId);
      if (updatedCattle != null && mounted) {
        // Update the cattle data in the parent widget
        widget.onCattleUpdated(updatedCattle);
        // Update local pregnancy state
        setState(() {
          _isPregnant = updatedCattle.breedingStatus?.isPregnant ?? false;
        });
      }

      // Load breeding records and store in state
      final records = await _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(cattleId);

      if (mounted) {
        setState(() {
          _breedingRecords = records;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading breeding data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Add method to get next breeding number for a cattle
  Future<int> _getNextBreedingNumber(String cattleId) async {
    try {
      final breedingRecords = await _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(cattleId);
      return breedingRecords.length + 1;
    } catch (e) {
      debugPrint('Error getting next breeding number: $e');
      return 1;
    }
  }



  // Method to get next delivery number for a cattle

  // Method to create delivery record ID

  // Method to create breeding record ID
  String _createBreedingRecordId(String cattleId, int sequenceNumber) {
    return '$cattleId-Breeding-$sequenceNumber';
  }

  // Helper methods for async operations
  Future<void> _checkAndUpdatePregnancyStatus() async {
    if (!mounted) return;

    // Check if we updated recently - avoid update loops
    final now = DateTime.now();
    if (_lastPregnancyStatusUpdateTime != null) {
      final timeSinceLastUpdate =
          now.difference(_lastPregnancyStatusUpdateTime!);
      if (timeSinceLastUpdate < _minimumUpdateInterval) {
        debugPrint(
            'Skipping pregnancy status update - too soon since last update (${timeSinceLastUpdate.inSeconds}s)');
        return;
      }
    }

    // Get all relevant pregnancy records
    final pregnancyRecords = await _databaseHelper.breedingHandler
        .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '');

    // Check if there are any active (Confirmed) pregnancies
    final hasActivePregnancy = pregnancyRecords
        .any((record) => record.status?.toLowerCase() == 'confirmed');

    // Check if there's a completed pregnancy (delivered)
    final hasCompletedPregnancy = pregnancyRecords.any((record) =>
        record.status?.toLowerCase() == 'completed' &&
        record.actualCalvingDate != null);

    // If there are no active pregnancies but there is a completed one,
    // make sure the cattle is not marked as pregnant
    if (!hasActivePregnancy &&
        hasCompletedPregnancy &&
        widget.cattle.breedingStatus?.isPregnant == true) {
      // The isPregnant flag is true but there are no active pregnancies, only completed ones
      debugPrint(
          'Fixing pregnancy status: cattle has completed pregnancy but no active pregnancies');

      // Important: Check if updates are already in progress by looking at the state data
      // This helps prevent infinite update cycles
      if (!_isLoading) {
        // Record this update time
        _lastPregnancyStatusUpdateTime = now;

        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = false
            ..status = 'Open'
            ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate,
        );

        // Update the cattle in the database
        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Update the UI through the callback instead of setState
        widget.onCattleUpdated(updatedCattle);

        // Update local pregnancy state immediately
        setState(() {
          _isPregnant = false;
        });
      }
    }

    // If there are no pregnancies at all but the cattle is marked as pregnant, fix that
    if (!hasActivePregnancy &&
        !hasCompletedPregnancy &&
        widget.cattle.breedingStatus?.isPregnant == true) {
      debugPrint(
          'Fixing pregnancy status: cattle has no pregnancies but is marked as pregnant');

      // Important: Check if updates are already in progress by looking at the state data
      if (!_isLoading) {
        // Record this update time
        _lastPregnancyStatusUpdateTime = now;

        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = false
            ..expectedCalvingDate = null
            ..status = 'Open'
            ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate ?? DateTime.now(),
        );

        // Update the cattle in the database
        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Update the UI through the callback instead of setState
        widget.onCattleUpdated(updatedCattle);

        // Update local pregnancy state immediately
        setState(() {
          _isPregnant = false;
        });
      }
    }

    // Force a refresh of the UI
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadBreedingData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Breeding Stats Card
                    _buildBreedingStatsCard(),

                    const SizedBox(height: 16),

                    // Breeding Eligibility Card - use state instead of FutureBuilder
                    Builder(
                      builder: (context) {
                        debugPrint('🏗️ BUILDING Breeding Eligibility Card for ${widget.cattle.tagId}');
                        debugPrint('🏗️ Current cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
                        debugPrint('🏗️ Current cattle status: ${widget.cattle.breedingStatus?.status}');
                        debugPrint('🏗️ Current cattle isPregnant: ${widget.cattle.breedingStatus?.isPregnant}');
                        debugPrint('🏗️ Local _isPregnant state: $_isPregnant');

                        return FutureBuilder(
                          future: _databaseHelper.farmSetupHandler.getAllAnimalTypes(),
                          builder: (context, snapshot) {
                            final animalTypes = snapshot.data ?? [];
                            final animalType = animalTypes.firstWhereOrNull(
                              (type) => type.businessId == widget.cattle.animalTypeId,
                            );

                            return EligibilityCard.breeding(
                              gender: widget.cattle.gender ?? '',
                              cattleId: widget.cattle.tagId ?? '',
                              animalTypeId: widget.cattle.animalTypeId ?? '',
                              isPregnant: _isPregnant,
                              dateOfBirth: widget.cattle.dateOfBirth,
                              purchaseDate: widget.cattle.purchaseDate,
                              lastBreedingDate: widget.cattle.breedingStatus?.breedingDate,
                              lastCalvingDate: widget.cattle.breedingStatus?.lastCalvingDate,
                              breedingRecords: _breedingRecords.map((record) => record.toMap()).toList(),
                              animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
                              onAddPressed: _isLoading ? null : () => _showAddBreedingForm(context),
                          trailing: IconButton(
                            icon: const Icon(Icons.info_outline, color: Color(0xFF2E7D32)),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Breeding Eligibility Information'),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text('This card shows the breeding eligibility status based on:'),
                                      const SizedBox(height: 8),
                                      const Text('• Gender (only females can be bred)'),
                                      const Text('• Age (minimum 24 months)'),
                                      const Text('• Pregnancy status'),
                                      const Text('• Previous breeding records'),
                                      const Text('• Required waiting periods'),
                                      if (widget.cattle.animalTypeId?.toLowerCase().contains('buffalo') ?? false) ...[
                                        const SizedBox(height: 8),
                                        const Text('Special rules for buffaloes:'),
                                        const Text('• 2 months between breeding attempts'),
                                        const Text('• 3 months rest after calving'),
                                      ],
                                    ],
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      child: const Text('Close'),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        );
                          },
                        );
                      },
                    ),

                    const SizedBox(height: 16),

                    // Breeding History Card
                    _buildBreedingHistoryCard(),

                    // Add padding at the bottom for the FAB
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddBreedingForm(context),
        backgroundColor: AppColors.primary,
        tooltip: 'Add Breeding Record',
        child: const Icon(Icons.add),
      ),
    );
  }

  // Add method to show breeding form with eligibility check
  Future<void> _showAddBreedingForm(BuildContext context) async {
    if (_isLoading) return;
    if (!mounted) return;

    // Store context before any async operations
    final currentContext = context;

    // Get breeding records to pass to the eligibility card
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(widget.cattle.tagId ?? '')
        .then((records) => records.map((record) => record.toMap()).toList());

    if (!mounted) return;

    // Check eligibility using the EligibilityCard widget's logic
    debugPrint('🔍 Breeding view eligibility check for ${widget.cattle.tagId}:');
    debugPrint('🔍 lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🔍 isPregnant: $_isPregnant');
    debugPrint('🔍 status: ${widget.cattle.breedingStatus?.status}');

    // Get animal type for empty period settings
    final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
    final animalType = animalTypes.firstWhereOrNull(
      (type) => type.businessId == widget.cattle.animalTypeId,
    );

    final eligibilityData = EligibilityCard.checkBreedingEligibility(
      gender: widget.cattle.gender ?? '',
      cattleId: widget.cattle.tagId ?? '',
      animalTypeId: widget.cattle.animalTypeId ?? '',
      isPregnant: _isPregnant,
      dateOfBirth: widget.cattle.dateOfBirth,
      purchaseDate: widget.cattle.purchaseDate,
      lastBreedingDate: widget.cattle.breedingStatus?.breedingDate,
      lastCalvingDate: widget.cattle.breedingStatus?.lastCalvingDate,
      breedingRecords: breedingRecords,
      animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
    );

    // Check if there's a pending breeding record
    bool hasPendingBreeding = false;
    if (breedingRecords.isNotEmpty) {
      hasPendingBreeding = breedingRecords.any(
          (record) => record['status']?.toString().toLowerCase() == 'pending');
    }

    // If the cattle is eligible and there's no pending breeding, show the form
    if (eligibilityData.isEligible && !hasPendingBreeding) {
      _showBreedingForm();
    } else {
      // Critical conditions where breeding should not be allowed
      if (mounted) {
        // Create the dialog widget before showing it
        final blockingDialog = AlertDialog(
          titlePadding: const EdgeInsets.all(16),
          contentPadding: const EdgeInsets.all(16),
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          title: Row(
            children: [
              Icon(
                eligibilityData.statusIcon,
                color: eligibilityData.statusColor,
                size: 20,
              ),
              const SizedBox(width: 20),
              Flexible(
                child: Text(
                  eligibilityData.statusMessage,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cattle: ${widget.cattle.name ?? 'Unknown'} (${widget.cattle.tagId ?? 'Unknown'})',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              const Text('You cannot add a new breeding record because:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text(eligibilityData.reasonMessage),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: eligibilityData.statusColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      eligibilityData.statusIcon,
                      color: eligibilityData.statusColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        eligibilityData.reasonMessage,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (eligibilityData.nextEligibleDateMessage != null) ...[
                const SizedBox(height: 12),
                Text(
                  eligibilityData.nextEligibleDateMessage!,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (mounted) {
                  Navigator.of(currentContext).pop();
                }
              },
              child: const Text('Dismiss'),
            ),
          ],
        );

        // Show dialog with the ineligibility reason
        if (mounted) {
          showDialog(
            // ignore: use_build_context_synchronously
            context: currentContext,
            builder: (dialogContext) => blockingDialog,
          );
        }
      }
    }
  }

  // Method to show the actual breeding form dialog
  Future<void> _showBreedingForm() async {
    if (!mounted) return;

    // Store context before any async operations
    final currentContext = context;

    try {
      // Preload data for the form dialog
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      final animalTypesMap = {for (var type in animalTypes) type.businessId ?? '': type};

      if (!mounted) return;

      // Store the dialog result in a local variable
      BreedingRecordIsar? result;
      if (mounted) {
        result = await showDialog<BreedingRecordIsar>(
          // ignore: use_build_context_synchronously
          context: currentContext,
          builder: (dialogContext) {
            return BreedingFormDialog(
              initialCattleId: widget.cattle.tagId,
              preloadedCattle: allCattle,
              preloadedAnimalTypes: animalTypesMap,
            );
          },
        );
      }

      // Check if widget is still mounted before proceeding
      if (mounted && result != null) {
        await _addBreedingRecord(result);
      }
    } catch (e) {
      if (mounted) {
        if (mounted) {
          // ignore: use_build_context_synchronously
          BreedingMessageUtils.showError(currentContext, BreedingMessageUtils.generalError(e.toString()));
        }
      }
    }
  }

  // Add method to show edit breeding form
  Future<void> _showEditBreedingForm(
      BuildContext context, Map<String, dynamic> record) async {
    if (_isLoading) return;
    if (!mounted) return;

    // Store context before async operations
    final currentContext = context;

    try {
      // Convert Map to BreedingRecordIsar
      final breedingRecord = BreedingRecordIsar.fromMap(record);

      // Preload data for the form dialog
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      final animalTypesMap = {for (var type in animalTypes) type.businessId ?? '': type};

      if (!mounted) return;

      BreedingRecordIsar? result;
      if (mounted) {
        result = await showDialog<BreedingRecordIsar>(
          // ignore: use_build_context_synchronously
          context: currentContext,
          builder: (dialogContext) {
            return BreedingFormDialog(
              record: breedingRecord,
              initialCattleId: widget.cattle.tagId,
              preloadedCattle: allCattle,
              preloadedAnimalTypes: animalTypesMap,
            );
          },
        );
      }

      // Check if widget is still mounted before proceeding
      if (mounted && result != null) {
        await _editBreedingRecord(breedingRecord.businessId!, result);
      }
    } catch (e) {
      if (mounted) {
        if (mounted) {
          // ignore: use_build_context_synchronously
          BreedingMessageUtils.showError(currentContext, BreedingMessageUtils.generalError(e.toString()));
        }
      }
    }
  }

  Widget _buildBreedingHistoryCard() {
    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Error: Cattle ID is missing'),
          ),
        ),
      );
    }

    if (_isLoading) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    // Convert breeding records to maps directly from state
    final breedingRecordMaps =
        _breedingRecords.map((record) => record.toMap()).toList();

    // Sort by date, most recent first - sorting is already done on _breedingRecords
    // but we'll ensure it here for consistency
    if (breedingRecordMaps.isNotEmpty) {
      breedingRecordMaps.sort((a, b) {
        final dateA = DateTime.parse(a['date']);
        final dateB = DateTime.parse(b['date']);
        return dateB.compareTo(dateA);
      });
    }

    // Use the reusable BreedingHistoryCard widget
    return BreedingHistoryCard(
      records: breedingRecordMaps,
      title: 'Breeding History',
      emptyMessage: 'No breeding history available',
      cattleName: widget.cattle.name,
      cattleId: widget.cattle.tagId,
      onEdit: (record) {
        if (mounted) {
          _showEditBreedingForm(context, record);
        }
      },
      onDelete: (record) {
        _deleteBreedingRecord(record);
      },
      onStatusTap: (record) {
        _showStatusChangeDialog(record);
      },
    );
  }

  // Helper method to get status color
  String _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return '#9C27B0'; // Purple
      case 'confirmed':
        return '#4CAF50'; // Green
      case 'pending':
        return '#2196F3'; // Blue
      case 'failed':
        return '#F44336'; // Red
      default:
        return '#757575'; // Grey
    }
  }

  // Add method to show status change dialog with improved UI
  Future<void> _showStatusChangeDialog(Map<String, dynamic> record) async {
    if (!mounted) return;

    final currentStatus = record['status']?.toString() ?? 'Unknown';
    String selectedStatus = currentStatus;
    final cattleName = widget.cattle.name ?? 'Unknown';
    final breedingDate = DateTime.parse(record['date'] as String);
    final cattleId = widget.cattle.tagId;
    final recordId = record['id'] as String;

    // Store context before any async operations
    final currentContext = context;

    try {
      if (cattleId == null || cattleId.isEmpty) {
        if (mounted) {
          BreedingMessageUtils.showError(currentContext, BreedingMessageUtils.cattleIdMissing);
        }
        return;
      }

      if (!mounted) return;

      final result = await showDialog<String>(
        context: currentContext,
        builder: (context) => AlertDialog(
          titlePadding: const EdgeInsets.all(16),
          title: Row(
            children: [
              Icon(
                Icons.edit_note,
                color: Color(
                  int.parse(
                        _getStatusColor(selectedStatus).substring(1),
                        radix: 16,
                      ) |
                      0xFF000000,
                ),
              ),
              const SizedBox(width: 8),
              const Flexible(
                child: Text(
                  'Update Breeding Status',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              final newStatusColor = _getStatusColor(selectedStatus);
              final newColor = Color(
                int.parse(
                      newStatusColor.substring(1),
                      radix: 16,
                    ) |
                    0xFF000000,
              );

              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show cattle info
                  Center(
                    child: Column(
                      children: [
                        Text(
                          'Cattle: $cattleName',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Tag ID: $cattleId',
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 8),

                  // Current status display
                  Row(
                    children: [
                      const Text(
                        'Current Status:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 3),
                          decoration: BoxDecoration(
                            color: Color(
                              int.parse(
                                    _getStatusColor(currentStatus).substring(1),
                                    radix: 16,
                                  ) |
                                  0xFF000000,
                            ).withAlpha(40),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Color(
                                int.parse(
                                      _getStatusColor(currentStatus).substring(1),
                                      radix: 16,
                                    ) |
                                    0xFF000000,
                              ).withAlpha(100),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: Text(
                                  currentStatus,
                                  style: TextStyle(
                                    color: Color(
                                      int.parse(
                                            _getStatusColor(currentStatus)
                                                .substring(1),
                                            radix: 16,
                                          ) |
                                          0xFF000000,
                                    ),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              if (selectedStatus != currentStatus) ...[
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.edit,
                                  size: 12,
                                  color: Color(
                                    int.parse(
                                          _getStatusColor(currentStatus)
                                              .substring(1),
                                          radix: 16,
                                        ) |
                                        0xFF000000,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // New status selection
                  const Text(
                    'Select new status:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.black87, // Use solid readable color
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...['Pending', 'Confirmed', 'Completed', 'Failed']
                      .map((status) {
                    final statusColor = _getStatusColor(status);
                    final color = Color(
                      int.parse(
                            statusColor.substring(1),
                            radix: 16,
                          ) |
                          0xFF000000,
                    );

                    return RadioListTile<String>(
                      title: Text(
                        status,
                        style: TextStyle(
                          color:
                              selectedStatus == status ? color : Colors.grey[700],
                          fontWeight: selectedStatus == status
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      value: status,
                      groupValue: selectedStatus,
                      activeColor: color,
                      onChanged: (value) {
                        setState(() {
                          selectedStatus = value!;
                        });
                      },
                      secondary: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: color.withAlpha(50),
                          shape: BoxShape.circle,
                          border: Border.all(color: color, width: 2),
                        ),
                      ),
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      dense: true,
                    );
                  }).toList(),

                  if (selectedStatus != currentStatus) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          'Preview:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.black87, // Use solid readable color
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Icon(
                          Icons.arrow_forward,
                          size: 16,
                          color: Colors.black87, // Use solid readable color
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 2),
                            decoration: BoxDecoration(
                              color: newColor.withAlpha(40),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: newColor.withAlpha(100)),
                            ),
                            child: Text(
                              selectedStatus,
                              style: TextStyle(
                                color: newColor,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(selectedStatus),
              style: TextButton.styleFrom(
                foregroundColor: Color(
                  int.parse(
                        _getStatusColor(selectedStatus).substring(1),
                        radix: 16,
                      ) |
                      0xFF000000,
                ),
              ),
              child: const Text('Update'),
            ),
          ],
        ),
      );

      if (result != null && result != currentStatus) {
        try {
          setState(() => _isLoading = true);

          // Calculate expected calving date if changing to confirmed or completed
          DateTime? expectedCalvingDate;
          if (result.toLowerCase() == 'confirmed' ||
              result.toLowerCase() == 'completed') {
            // Get animal type for this cattle to calculate expected calving date
            final animalTypes =
                await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
            final animalType = animalTypes.firstWhere(
              (type) => type.businessId == widget.cattle.animalTypeId,
              orElse: () => animalTypes.first,
            );

            // Calculate expected calving date
            expectedCalvingDate = breedingDate.add(
              Duration(days: animalType.defaultGestationDays ?? 283),
            );
          }

          // Create updated record with new status
          final updatedRecordMap = Map<String, dynamic>.from(record);
          updatedRecordMap['status'] = result;
          updatedRecordMap['expectedDate'] =
              expectedCalvingDate?.toIso8601String();

          // Update the record
          await _databaseHelper.breedingHandler
              .updateBreedingRecordFromMap(updatedRecordMap);

          // Update local state
          if (mounted) {
            setState(() {
              // Find and update the record in the list
              final index = _breedingRecords
                  .indexWhere((r) => r.id.toString() == recordId.toString());
              if (index >= 0) {
                // Create updated record
                final updatedRecord = _breedingRecords[index].copyWith(
                  status: result,
                  expectedDate: expectedCalvingDate,
                );
                _breedingRecords[index] = updatedRecord;
                _isLoading = false;
              }
            });
          }

          // Check if we need to create/update a pregnancy record when changing to Confirmed or Completed
          if (result.toLowerCase() == 'confirmed' ||
              result.toLowerCase() == 'completed') {
            // Get any existing pregnancy record linked to this breeding record
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(cattleId);

            try {
              final existingPregnancy = pregnancyRecords.firstWhere(
                (pr) => pr.breedingRecordId == recordId,
              );

              // If pregnancy record exists, update it
              final updatedPregnancy = existingPregnancy.copyWith(
                status: result,
                expectedCalvingDate: expectedCalvingDate,
                actualCalvingDate:
                    result.toLowerCase() == 'completed' ? DateTime.now() : null,
                endDate:
                    result.toLowerCase() == 'completed' ? DateTime.now() : null,
              );

              // Update pregnancy record
              await _databaseHelper.breedingHandler
                  .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);
            } catch (e) {
              // Create new pregnancy record if none exists
              final pregnancyRecord = PregnancyRecordIsar.create(
                cattleId: cattleId,
                startDate: breedingDate,
                status: result,
                expectedCalvingDate: expectedCalvingDate,
                actualCalvingDate:
                    result.toLowerCase() == 'completed' ? DateTime.now() : null,
                endDate:
                    result.toLowerCase() == 'completed' ? DateTime.now() : null,
                notes:
                    'Created from breeding record: ${record['method']} on ${DateFormat('MMMM dd, yyyy').format(breedingDate)}',
                breedingRecordId: recordId,
              );

              // Save pregnancy record
              await _databaseHelper.breedingHandler
                  .managePregnancyRecord(pregnancyRecord);
            }

            // Update cattle pregnancy status - IMPORTANT: For 'Completed' status, set isPregnant to false
            final breedingStatus = BreedingStatus()
              ..isPregnant = result.toLowerCase() == 'confirmed'
              ..breedingDate = breedingDate
              ..expectedCalvingDate =
                  result.toLowerCase() == 'confirmed' ? expectedCalvingDate : null
              ..status =
                  result.toLowerCase() == 'completed' ? 'Open' : 'Pregnant'
              ..lastCalvingDate = result.toLowerCase() == 'completed' ? DateTime.now() : widget.cattle.breedingStatus?.lastCalvingDate;

            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: breedingStatus,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);
            
            // Update local pregnancy state immediately
            setState(() {
              _isPregnant = result.toLowerCase() == 'confirmed';
            });
          }
          // Handle pregnancy status update when changing from Confirmed/Completed to Pending/Failed
          else if ((currentStatus.toLowerCase() == 'confirmed' ||
                  currentStatus.toLowerCase() == 'completed') &&
              (result.toLowerCase() == 'pending' ||
                  result.toLowerCase() == 'failed')) {
            // Get any existing pregnancy record linked to this breeding record
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(cattleId);

            try {
              final existingPregnancy = pregnancyRecords.firstWhere(
                (pr) => pr.breedingRecordId == recordId,
              );

              // Update pregnancy status to match breeding status instead of deleting
              final updatedPregnancy = existingPregnancy.copyWith(
                status: result.toLowerCase() == 'pending' ? 'Pending' : 'Failed',
              );

              await _databaseHelper.breedingHandler
                  .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);

              // Update cattle status if this was the current pregnancy
              if (widget.cattle.breedingStatus?.isPregnant == true) {
                // Check if there are any other active pregnancies
                final otherActivePregnancies = pregnancyRecords
                    .where((pr) =>
                        pr.id != existingPregnancy.id &&
                        pr.status?.toLowerCase() == 'confirmed')
                    .toList();

                if (otherActivePregnancies.isEmpty) {
                  // No other active pregnancies, update cattle status
                  final breedingStatus = BreedingStatus()
                    ..isPregnant = false
                    ..expectedCalvingDate = null
                    ..status = 'Open'
                    ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate;

                  final updatedCattle = widget.cattle.copyWith(
                    breedingStatus: breedingStatus,
                  );

                  await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
                  widget.onCattleUpdated(updatedCattle);

                  // Update local pregnancy state immediately
                  setState(() {
                    _isPregnant = false;
                  });
                }
              }
            } catch (e) {
              debugPrint('No pregnancy record found to update: $e');
              // No pregnancy record found, which is fine in this case
            }
          }

          // Wait a brief moment to ensure all database operations are committed
          await Future.delayed(const Duration(milliseconds: 100));

          // Update local state one more time to ensure consistency
          final finalIsPregnant = result.toLowerCase() == 'confirmed' ||
                                 (result.toLowerCase() != 'pending' &&
                                  result.toLowerCase() != 'failed' &&
                                  widget.cattle.breedingStatus?.isPregnant == true);

          setState(() {
            _isPregnant = finalIsPregnant;
          });

          // Check and fix any inconsistencies in pregnancy status, but skip if we just updated
          // to avoid overriding the state we just set
          if (result.toLowerCase() != 'confirmed') {
            await _checkAndUpdatePregnancyStatus();
          }

          if (!mounted) return;
          final message = BreedingMessageUtils.breedingStatusUpdated(result);
          BreedingMessageUtils.showSuccess(context, message);
        } catch (e) {
          if (mounted) {
            setState(() => _isLoading = false);
            BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, BreedingMessageUtils.generalError(e.toString()));
      }
    }
  }

  // Build breeding stats card optimized to use _breedingRecords directly
  Widget _buildBreedingStatsCard() {
    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Error: Cattle ID is missing'),
          ),
        ),
      );
    }

    if (_isLoading) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    // Calculate statistics directly from the _breedingRecords state
    final successfulBreedings = _breedingRecords
        .where((record) => (record.status?.toLowerCase() ?? '') == 'completed')
        .length;
    final failedBreedings = _breedingRecords
        .where((record) => (record.status?.toLowerCase() ?? '') == 'failed')
        .length;
    final pendingBreedings = _breedingRecords
        .where((record) => (record.status?.toLowerCase() ?? '') == 'pending')
        .length;
    final confirmedBreedings = _breedingRecords
        .where((record) => (record.status?.toLowerCase() ?? '') == 'confirmed')
        .length;
    final totalBreedings = _breedingRecords.length;

    // Use the factory method for StatsCard
    return StatsCard.breedingStats(
      totalBreedings: totalBreedings,
      pendingBreedings: pendingBreedings,
      confirmedBreedings: confirmedBreedings,
      completedBreedings: successfulBreedings,
      failedBreedings: failedBreedings,
      onViewAllTap: () {
        // Optional: Navigate to detailed breeding records view
      },
      onInfoTap: () {
        // Show breeding stats information
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Breeding Statistics Info'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'These statistics show the breeding history for this cattle:'),
                SizedBox(height: 8),
                Text('• Total: All breeding attempts for this cattle'),
                Text('• Pending: Breeding attempts awaiting confirmation'),
                Text('• Confirmed: Confirmed pregnancies awaiting delivery'),
                Text(
                    '• Completed: Successfully completed breedings with calving'),
                Text('• Failed: Unsuccessful breeding attempts'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      },
    );
  }

  // Add breeding record method with automatic pregnancy record creation
  Future<void> _addBreedingRecord(BreedingRecordIsar record) async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    final cattleId = widget.cattle.tagId;
    if (cattleId == null || cattleId.isEmpty) {
      setState(() => _isLoading = false);
      BreedingMessageUtils.showError(context, BreedingMessageUtils.cattleIdMissing);
      return;
    }

    try {
      // Get the next sequence number
      final sequenceNumber = await _getNextBreedingNumber(cattleId);

      // Create ID for the breeding record
      final breedingRecordId =
          _createBreedingRecordId(cattleId, sequenceNumber);

      // Set the business ID if not already set
      BreedingRecordIsar breedingRecord;
      if (record.businessId == null) {
        breedingRecord = record.copyWith(businessId: breedingRecordId);
      } else {
        breedingRecord = record;
      }

      // Add the record to the database with automatic pregnancy record creation
      final status = breedingRecord.status?.toLowerCase() ?? '';
      final shouldCreatePregnancy = status == 'confirmed' || status == 'completed';

      await _databaseHelper.breedingHandler.addBreedingRecord(
        breedingRecord,
        createPregnancyRecord: shouldCreatePregnancy,
      );

      // Update local state with the new record
      if (mounted) {
        setState(() {
          _breedingRecords.add(breedingRecord);
          // Sort records by date, most recent first
          _breedingRecords.sort((a, b) =>
              (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
          _isLoading = false;
        });
      }

      // If pregnancy record was created, update cattle status
      if (shouldCreatePregnancy) {
        // Force a complete UI refresh by getting fresh cattle data after pregnancy creation
        final freshCattle = await _databaseHelper.cattleHandler
            .getCattleByTagId(widget.cattle.tagId ?? '');
        if (freshCattle != null) {
          widget.onCattleUpdated(freshCattle);
          // Update local pregnancy state immediately
          setState(() {
            _isPregnant = freshCattle.breedingStatus?.isPregnant ?? false;
          });
        }

        // Show success message with pregnancy info
        if (mounted) {
          final message = BreedingMessageUtils.breedingRecordCreated(withPregnancy: true);
          BreedingMessageUtils.showSuccess(context, message);
        }
      } else {
        if (mounted) {
          final message = BreedingMessageUtils.breedingRecordCreated(withPregnancy: false);
          BreedingMessageUtils.showSuccess(context, message);
        }
      }
    } catch (e) {
      debugPrint('Error adding breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // Edit breeding record method with pregnancy record synchronization
  Future<void> _editBreedingRecord(
      String recordId, BreedingRecordIsar updatedRecord) async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      // Ensure the record has the correct ID
      BreedingRecordIsar recordToUpdate = updatedRecord.businessId == recordId
          ? updatedRecord
          : updatedRecord.copyWith(businessId: recordId);

      // Get the original record to check for status changes
      final originalRecord =
          await _databaseHelper.breedingHandler.getBreedingRecordById(recordId);
      final originalStatus = originalRecord?.status?.toLowerCase() ?? '';
      final newStatus = recordToUpdate.status?.toLowerCase() ?? '';
      final breedingDate = recordToUpdate.date ?? DateTime.now();
      final cattleId = widget.cattle.tagId;

      if (cattleId == null || cattleId.isEmpty) {
        throw Exception('Cattle ID is missing');
      }

      // Update the record in the database
      await _databaseHelper.breedingHandler
          .updateBreedingRecord(recordToUpdate);

      // Update local state
      if (mounted) {
        setState(() {
          // Find and replace the record in the list
          final index = _breedingRecords
              .indexWhere((r) => r.id.toString() == recordId.toString());
          if (index >= 0) {
            _breedingRecords[index] = recordToUpdate;
          }
          // Sort records by date, most recent first
          _breedingRecords.sort((a, b) =>
              (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
          _isLoading = false;
        });
      }

      // Handle pregnancy record updates based on status changes
      if (newStatus != originalStatus) {
        // Calculate expected calving date if needed
        DateTime? expectedCalvingDate;
        if (updatedRecord.expectedDate != null) {
          expectedCalvingDate = updatedRecord.expectedDate;
        } else if (newStatus == 'confirmed' || newStatus == 'completed') {
          // Calculate expected calving date
          final animalTypes =
              await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
          final animalType = animalTypes.firstWhere(
            (type) => type.businessId == widget.cattle.animalTypeId,
            orElse: () => animalTypes.first,
          );

          expectedCalvingDate = breedingDate.add(
            Duration(days: animalType.defaultGestationDays ?? 283),
          );
        }

        // Get any existing pregnancy records for this breeding record
        final pregnancyRecords = await _databaseHelper.breedingHandler
            .getPregnancyRecordsForCattle(cattleId);
        final existingPregnancy = pregnancyRecords.firstWhereOrNull(
          (pr) => pr.breedingRecordId == recordId,
        );

        // Handle status transitions
        if ((newStatus == 'confirmed' || newStatus == 'completed') &&
            (originalStatus == 'pending' || originalStatus == 'failed')) {
          // Transition to confirmed/completed - create or update pregnancy record
          if (existingPregnancy != null) {
            // Update existing pregnancy record
            final updatedPregnancy = existingPregnancy.copyWith(
              status: recordToUpdate.status ?? 'Active',
              expectedCalvingDate: expectedCalvingDate,
              actualCalvingDate:
                  newStatus == 'completed' ? DateTime.now() : null,
              endDate: newStatus == 'completed' ? DateTime.now() : null,
            );

            await _databaseHelper.breedingHandler
                .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);
          } else {
            // Create new pregnancy record
            final pregnancyRecord = PregnancyRecordIsar.create(
              cattleId: cattleId,
              startDate: breedingDate,
              status: recordToUpdate.status ?? 'Active',
              expectedCalvingDate: expectedCalvingDate,
              actualCalvingDate:
                  newStatus == 'completed' ? DateTime.now() : null,
              endDate: newStatus == 'completed' ? DateTime.now() : null,
              notes:
                  'Created from updated breeding record: ${recordToUpdate.method} on ${DateFormat('MMMM dd, yyyy').format(breedingDate)}',
              breedingRecordId: recordId,
            );

            await _databaseHelper.breedingHandler
                .managePregnancyRecord(pregnancyRecord);
          }

          // Update cattle status
          final breedingStatus = BreedingStatus()
            ..isPregnant = newStatus == 'confirmed'
            ..breedingDate = breedingDate
            ..expectedCalvingDate =
                newStatus == 'confirmed' ? expectedCalvingDate : null
            ..status = newStatus == 'completed' ? 'Open' : 'Pregnant'
            ..lastCalvingDate = newStatus == 'completed' ? DateTime.now() : widget.cattle.breedingStatus?.lastCalvingDate;

            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: breedingStatus,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);

            // Update local pregnancy state immediately
            setState(() {
              _isPregnant = newStatus == 'confirmed';
            });
        } else if ((originalStatus == 'confirmed' ||
                originalStatus == 'completed') &&
            (newStatus == 'pending' || newStatus == 'failed')) {
          // Transition from confirmed/completed to pending/failed - update pregnancy status
          if (existingPregnancy != null) {
            final updatedPregnancy = existingPregnancy.copyWith(
              status: newStatus == 'pending' ? 'Pending' : 'Failed',
            );

            await _databaseHelper.breedingHandler
                .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);

            // Check if there are any other active pregnancies
            final otherActivePregnancies = pregnancyRecords
                .where((pr) =>
                    pr.id != existingPregnancy.id &&
                    pr.status?.toLowerCase() == 'confirmed')
                .toList();

            if (otherActivePregnancies.isEmpty &&
                widget.cattle.breedingStatus?.isPregnant == true) {
              // No other active pregnancies, update cattle status
              final breedingStatus = BreedingStatus()
                ..isPregnant = false
                ..expectedCalvingDate = null
                ..status = 'Open'
                ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate;

              final updatedCattle = widget.cattle.copyWith(
                breedingStatus: breedingStatus,
              );

              await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
              widget.onCattleUpdated(updatedCattle);

              // Update local pregnancy state immediately
              setState(() {
                _isPregnant = false;
              });
            }
          }
        } else if (newStatus == 'confirmed' && originalStatus == 'completed') {
          // Transition from completed to confirmed - update pregnancy record
          if (existingPregnancy != null) {
            final updatedPregnancy = existingPregnancy.copyWith(
              status: 'Confirmed',
              expectedCalvingDate: expectedCalvingDate,
              actualCalvingDate: null,
              endDate: null,
            );

            await _databaseHelper.breedingHandler
                .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);

            // Update cattle status
            final breedingStatus = BreedingStatus()
              ..isPregnant = true
              ..breedingDate = breedingDate
              ..expectedCalvingDate = expectedCalvingDate
              ..status = 'Pregnant'
              ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate;

            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: breedingStatus,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);

            // Update local pregnancy state immediately
            setState(() {
              _isPregnant = true;
            });
          }
        } else if (newStatus == 'completed' && originalStatus == 'confirmed') {
          // Transition from confirmed to completed - update pregnancy record
          if (existingPregnancy != null) {
            final updatedPregnancy = existingPregnancy.copyWith(
              status: 'Completed',
              actualCalvingDate: DateTime.now(),
              endDate: DateTime.now(),
            );

            await _databaseHelper.breedingHandler
                .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: true);

            // Update cattle status
            final breedingStatus = BreedingStatus()
              ..isPregnant = false
              ..lastCalvingDate = DateTime.now()
              ..breedingDate = breedingDate
              ..expectedCalvingDate = null
              ..status = 'Open';

            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: breedingStatus,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);

            // Update local pregnancy state immediately
            setState(() {
              _isPregnant = false;
            });
          }
        }
      }

      // Wait a brief moment to ensure all database operations are committed
      await Future.delayed(const Duration(milliseconds: 100));

      // Update local state one more time to ensure consistency
      final finalIsPregnant = newStatus == 'confirmed' ||
                             (newStatus != 'pending' &&
                              newStatus != 'failed' &&
                              newStatus != 'completed' &&
                              widget.cattle.breedingStatus?.isPregnant == true);

      setState(() {
        _isPregnant = finalIsPregnant;
      });

      // Check and fix any inconsistencies in pregnancy status, but skip if we just updated
      // to avoid overriding the state we just set
      if (newStatus != 'confirmed') {
        await _checkAndUpdatePregnancyStatus();
      }

      if (mounted) {
        final message = BreedingMessageUtils.breedingRecordUpdated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      debugPrint('Error editing breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // Delete breeding record method with cascading deletion
  Future<void> _deleteBreedingRecord(Map<String, dynamic> record) async {
    if (!mounted) return;

    final recordId = record['id'];
    final cattleId = widget.cattle.tagId;

    if (recordId == null) {
      if (mounted) {
        BreedingMessageUtils.showError(context, BreedingMessageUtils.breedingRecordIdMissing);
      }
      return;
    }

    if (cattleId == null || cattleId.isEmpty) {
      if (mounted) {
        BreedingMessageUtils.showError(context, BreedingMessageUtils.cattleIdMissing);
      }
      return;
    }

    // Find associated records for confirmation message
    List<String> associatedRecords = [];

    // Get pregnancy records for this cattle
    final pregnancyRecords = await _databaseHelper.breedingHandler
        .getPregnancyRecordsForCattle(cattleId);

    // Find any pregnancy record associated with this breeding record
    final associatedPregnancy = pregnancyRecords.firstWhereOrNull(
      (pr) => pr.breedingRecordId == recordId,
    );

    if (associatedPregnancy != null) {
      associatedRecords.add(
          '$cattleId-Pregnancy-${associatedPregnancy.businessId?.split('-').last ?? ''}');
    }

    // Get delivery records for this cattle
    final deliveryRecords = await _databaseHelper.breedingHandler
        .getDeliveryRecordsForCattle(cattleId);

    // Find any delivery record associated with this pregnancy
    final associatedDelivery = associatedPregnancy != null
        ? deliveryRecords.firstWhereOrNull(
            (dr) => dr['pregnancyId'] == associatedPregnancy['id'],
          )
        : null;

    if (associatedDelivery != null) {
      final deliveryId = associatedDelivery['id'] as String?;
      if (deliveryId != null) {
        associatedRecords.add(deliveryId);
      }
    }

    // Show standardized confirmation dialog
    if (!mounted) return;
    final cattleDisplayName = widget.cattle.name != null && widget.cattle.tagId != null
        ? '${widget.cattle.name} (${widget.cattle.tagId})'
        : widget.cattle.name ?? widget.cattle.tagId ?? 'Unknown';
    final confirmed = await BreedingMessageUtils.showBreedingDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: recordId,
      pregnancyRecords: associatedPregnancy != null ? 1 : 0,
      deliveryRecords: associatedDelivery != null ? 1 : 0,
      specificRecords: associatedRecords,
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      // Implement cascading deletion

      // 1. Delete associated delivery record first (if exists)
      if (associatedDelivery != null) {
        final deliveryId = associatedDelivery['id'] as String?;
        if (deliveryId != null) {
          await _databaseHelper.breedingHandler
              .deleteDeliveryRecord(deliveryId);
        }
      }

      // 2. Delete associated pregnancy record (if exists)
      if (associatedPregnancy != null) {
        await _databaseHelper.breedingHandler.managePregnancyRecord(
            associatedPregnancy.copyWith(status: 'Deleted'));

        // Check if this was the current active pregnancy
        if (widget.cattle.breedingStatus?.isPregnant == true) {
          // Check if there are any other active pregnancies
          final otherActivePregnancies = pregnancyRecords
              .where((pr) =>
                  pr.id != associatedPregnancy.id &&
                  pr.status?.toLowerCase() == 'confirmed')
              .toList();

          if (otherActivePregnancies.isEmpty) {
            // No other active pregnancies, update cattle status
            final breedingStatus = BreedingStatus()
              ..isPregnant = false
              ..expectedCalvingDate = null
              ..status = 'Open'
              ..lastCalvingDate = widget.cattle.breedingStatus?.lastCalvingDate;

            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: breedingStatus,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);

            // Update local pregnancy state immediately
            setState(() {
              _isPregnant = false;
            });
          }
        }
      }

      // 3. Delete the breeding record from the database
      await _databaseHelper.breedingHandler.deleteBreedingRecord(recordId);

      // Update local state
      if (mounted) {
        setState(() {
          // Remove the record from the list
          final initialCount = _breedingRecords.length;
          _breedingRecords.removeWhere((r) => r.businessId == recordId || r.id.toString() == recordId);
          final finalCount = _breedingRecords.length;
          debugPrint('Directly removed ${initialCount - finalCount} breeding records');
          _isLoading = false;
        });

        // Force rebuild of the history card
        setState(() {});
      }

      // Wait a brief moment to ensure all database operations are committed
      await Future.delayed(const Duration(milliseconds: 100));

      // Update local state one more time to ensure consistency
      setState(() {
        _isPregnant = widget.cattle.breedingStatus?.isPregnant ?? false;
      });

      // Check and fix any inconsistencies in pregnancy status
      await _checkAndUpdatePregnancyStatus();

      if (mounted) {
        setState(() => _isLoading = false);

        // Show standardized success message
        final message = BreedingMessageUtils.breedingRecordDeleted(
          pregnancyRecords: associatedPregnancy != null ? 1 : 0,
          deliveryRecords: associatedDelivery != null ? 1 : 0,
          specificRecords: associatedRecords,
        );
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      debugPrint('Error deleting breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showBreedingError(context, 'deleting', e.toString());
      }
    }
  }

  // Helper method for status colors
}
