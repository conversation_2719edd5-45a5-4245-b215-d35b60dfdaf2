import 'package:flutter/material.dart';
import '../models/cattle_isar.dart';
import 'breeding/breeding_view.dart';
import 'breeding/pregnancy_view.dart';
import 'breeding/delivery_view.dart';

class BreedingTab extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;
  final int initialBreedingTabIndex;

  const BreedingTab({
    Key? key,
    required this.cattle,
    required this.onCattleUpdated,
    this.initialBreedingTabIndex = 0,
  }) : super(key: key);

  @override
  State<BreedingTab> createState() => _BreedingTabState();
}

class _BreedingTabState extends State<BreedingTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    debugPrint('🔄 BreedingTab initState for ${widget.cattle.tagId}');
    debugPrint('🔄 Initial cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: widget.initialBreedingTabIndex,
    );

    // Add listener to update UI when tab changes
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(BreedingTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    debugPrint('🔄 BreedingTab didUpdateWidget for ${widget.cattle.tagId}');
    debugPrint('🔄 Old cattle lastCalvingDate: ${oldWidget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🔄 New cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
  }

  void _handleTabChange() {
    // Only rebuild if the tab index changed and widget is mounted
    if (_tabController.indexIsChanging && mounted) {
      setState(() {});
    }
  }

  // Get the indicator color based on the selected tab
  Color _getIndicatorColor() {
    switch (_tabController.index) {
      case 0:
        return const Color(0xFF1976D2); // Blue for breeding
      case 1:
        return const Color(0xFF6A1B9A); // Purple for pregnancy
      case 2:
        return const Color(0xFF2E7D32); // Green for delivery
      default:
        return const Color(0xFF1976D2); // Default to blue
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        toolbarHeight: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: null, // We'll set colors per tab
          unselectedLabelColor: null, // We'll set colors per tab
          indicatorColor: _getIndicatorColor(),
          tabs: [
            Tab(
              icon: Icon(
                Icons.calendar_today,
                color: _tabController.index == 0
                    ? const Color(0xFF1976D2) // Blue for breeding
                    : const Color(0xFF1976D2).withValues(alpha: 0.7),
              ),
              child: Text(
                "Breeding",
                style: TextStyle(
                  color: _tabController.index == 0
                      ? const Color(0xFF1976D2) // Blue for breeding
                      : const Color(0xFF1976D2).withValues(alpha: 0.7),
                  fontWeight: _tabController.index == 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            Tab(
              icon: Icon(
                Icons.pets, // Use cattle-appropriate icon instead of pregnant_woman
                color: _tabController.index == 1
                    ? const Color(0xFF6A1B9A) // Purple for pregnancy
                    : const Color(0xFF6A1B9A).withValues(alpha: 0.7),
              ),
              child: Text(
                "Pregnancy",
                style: TextStyle(
                  color: _tabController.index == 1
                      ? const Color(0xFF6A1B9A) // Purple for pregnancy
                      : const Color(0xFF6A1B9A).withValues(alpha: 0.7),
                  fontWeight: _tabController.index == 1 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            Tab(
              icon: Icon(
                Icons.child_care,
                color: _tabController.index == 2
                    ? const Color(0xFF2E7D32) // Green for delivery
                    : const Color(0xFF2E7D32).withValues(alpha: 0.7),
              ),
              child: Text(
                "Delivery",
                style: TextStyle(
                  color: _tabController.index == 2
                      ? const Color(0xFF2E7D32) // Green for delivery
                      : const Color(0xFF2E7D32).withValues(alpha: 0.7),
                  fontWeight: _tabController.index == 2 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        physics: const ClampingScrollPhysics(),
        children: [
          // Breeding Tab
          BreedingView(
            key: const ValueKey('breeding_view'),
            cattle: widget.cattle,
            onCattleUpdated: widget.onCattleUpdated,
          ),
          // Pregnancy Tab
          PregnancyView(
            key: const ValueKey('pregnancy_view'),
            cattle: widget.cattle,
            onCattleUpdated: widget.onCattleUpdated,
          ),
          // Delivery Tab
          DeliveryView(
            key: const ValueKey('delivery_view'),
            cattle: widget.cattle,
            onCattleUpdated: widget.onCattleUpdated,
          ),
        ],
      ),
    );
  }
}
