import 'package:flutter/material.dart';
import '../../Transactions/models/transaction_isar.dart';
import '../models/transactions_report_data_isar.dart';
import '../report_tabs/transaction_summary_tab.dart';
import '../report_tabs/transaction_details_tab.dart';

class TransactionsReportScreen extends StatefulWidget {
  const TransactionsReportScreen({Key? key}) : super(key: key);

  @override
  TransactionsReportScreenState createState() =>
      TransactionsReportScreenState();
}

class TransactionsReportScreenState extends State<TransactionsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<TransactionIsar> transactions = [];
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCategory;
  String? selectedType;
  bool isLoading = true;
  String? errorMessage;
  TransactionsReportDataIsar? reportData;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    endDate = DateTime.now();
    startDate = endDate!.subtract(const Duration(days: 30));
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Create an empty report instead of querying the database
      // We need to add a TransactionsReportDataIsar.empty() constructor
      // in the model file, but for now we can just create a simple
      // instance with default values

      setState(() {
        // Call a method to create a dummy report
        reportData = _createEmptyReport();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load transaction records: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper method to create an empty report
  TransactionsReportDataIsar _createEmptyReport() {
    // Note: In real implementation, we would add an .empty() constructor to the model
    final report = TransactionsReportDataIsar();
    report.initializeReport(
      reportType: 'transactions',
      title: 'Transactions Report',
      startDate: startDate,
      endDate: endDate,
      filterCriteria: '',
    );
    return report;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                TransactionSummaryTab(reportData: reportData!),
                TransactionDetailsTab(reportData: reportData!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Start Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: startDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() => startDate = date);
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'End Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: endDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() => endDate = date);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
