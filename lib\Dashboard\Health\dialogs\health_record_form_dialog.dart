import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../../../utils/message_utils.dart';

class HealthRecordFormDialog extends StatefulWidget {
  final HealthRecordIsar? healthRecord;
  final List<CattleIsar> cattle;
  final Future<void> Function(HealthRecordIsar)? onSave;

  const HealthRecordFormDialog({
    Key? key,
    this.healthRecord,
    required this.cattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<HealthRecordFormDialog> createState() => _HealthRecordFormDialogState();
}

class _HealthRecordFormDialogState extends State<HealthRecordFormDialog> {
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding =
      EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const _animationDuration = Duration(milliseconds: 200);

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  String? _cattleId = '';
  String? _condition = '';
  String _treatment = '';
  String _veterinarian = '';
  DateTime _date = DateTime.now();
  double _cost = 0.0;
  String _notes = '';

  List<CattleIsar> _cattleList = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadCattleData();
    if (widget.healthRecord != null) {
      _cattleId = widget.healthRecord!.cattleId;
      _condition = widget.healthRecord!.condition ?? '';
      _treatment = widget.healthRecord!.treatment ?? '';
      _veterinarian = widget.healthRecord!.veterinarian ?? '';
      _date = widget.healthRecord!.date ?? DateTime.now();
      _cost = widget.healthRecord!.cost ?? 0.0;
      _notes = widget.healthRecord!.notes ?? '';
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadCattleData() async {
    try {
      if (mounted) {
        setState(() {
          _cattleList = widget.cattle;
          if (_cattleList.isNotEmpty && _cattleId?.isEmpty == true) {
            _cattleId = _cattleList.first.tagId;
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        HealthMessageUtils.showError(context,
            'Could not load cattle information. Please try again.');
      }
    }
  }

  Future<void> _saveRecord() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isSaving = true;
      });

      try {
        final record = HealthRecordIsar.create(
          recordId: widget.healthRecord?.recordId,
          cattleId: _cattleId ?? '',
          date: _date,
          diagnosis: _condition,
          treatment: _treatment,
          notes: _notes,
          cost: _cost,
          veterinarian: _veterinarian,
        );

        if (mounted) {
          if (widget.onSave != null) {
            // Use the onSave callback if provided
            await widget.onSave!(record);
            if (mounted) {
              setState(() {
                _isSaving = false;
              });
              Navigator.pop(context);
            }
          } else {
            // Return the record object directly
            setState(() {
              _isSaving = false;
            });
            Navigator.pop(context, record);
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
          HealthMessageUtils.showError(context,
              'Failed to save health record. Please check your inputs and try again.');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: AnimatedContainer(
        duration: _animationDuration,
        curve: Curves.easeInOut,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Color(0xFF2E7D32),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          widget.healthRecord == null
                              ? Icons.add_circle
                              : Icons.edit,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.healthRecord == null
                              ? 'Add Health Record'
                              : 'Edit Health Record',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Form
                  Flexible(
                    child: Scrollbar(
                      controller: _scrollController,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Cattle Dropdown
                              DropdownButtonFormField<String>(
                                value: _cattleId?.isNotEmpty == true &&
                                        _cattleList
                                            .any((c) => c.tagId == _cattleId)
                                    ? _cattleId
                                    : _cattleList.isNotEmpty
                                        ? _cattleList.first.tagId
                                        : null,
                                decoration: InputDecoration(
                                  labelText: 'Cattle',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.pets,
                                    color: Colors.brown,
                                  ),
                                ),
                                items: _cattleList
                                    .map((cattle) => DropdownMenuItem<String>(
                                          value: cattle.tagId,
                                          child: Text(
                                            '${cattle.name} (${cattle.tagId})',
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ))
                                    .toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _cattleId = value;
                                    });
                                  }
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a cattle';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              // Date Picker
                              InkWell(
                                onTap: () async {
                                  final pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: _date,
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime.now()
                                        .add(const Duration(days: 365)),
                                  );
                                  if (pickedDate != null && mounted) {
                                    setState(() {
                                      _date = pickedDate;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'Date',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    contentPadding: _inputContentPadding,
                                    constraints: _inputDecorationConstraints,
                                    prefixIcon: const Icon(
                                      Icons.calendar_today,
                                      color: Colors.blue,
                                    ),
                                  ),
                                  child: Text(
                                    '${_date.day}/${_date.month}/${_date.year}',
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              // Condition
                              TextFormField(
                                initialValue: _condition,
                                decoration: InputDecoration(
                                  labelText: 'Condition/Diagnosis',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.medical_information,
                                    color: Colors.red,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a condition';
                                  }
                                  return null;
                                },
                                onSaved: (value) {
                                  _condition = value ?? '';
                                },
                              ),
                              const SizedBox(height: 16),
                              // Treatment
                              TextFormField(
                                initialValue: _treatment,
                                decoration: InputDecoration(
                                  labelText: 'Treatment',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.healing,
                                    color: Colors.green,
                                  ),
                                ),
                                onSaved: (value) {
                                  _treatment = value ?? '';
                                },
                              ),
                              const SizedBox(height: 16),
                              // Veterinarian
                              TextFormField(
                                initialValue: _veterinarian,
                                decoration: InputDecoration(
                                  labelText: 'Veterinarian',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.person,
                                    color: Colors.indigo,
                                  ),
                                ),
                                onSaved: (value) {
                                  _veterinarian = value ?? '';
                                },
                              ),
                              const SizedBox(height: 16),
                              // Cost
                              TextFormField(
                                initialValue: _cost.toString(),
                                decoration: InputDecoration(
                                  labelText: 'Cost',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixText: '\$ ',
                                ),
                                keyboardType: TextInputType.number,
                                onSaved: (value) {
                                  _cost = double.tryParse(value ?? '0') ?? 0.0;
                                },
                              ),
                              const SizedBox(height: 16),
                              // Notes
                              TextFormField(
                                initialValue: _notes,
                                decoration: InputDecoration(
                                  labelText: 'Notes',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.all(16),
                                  prefixIcon: const Icon(
                                    Icons.note_alt,
                                    color: Colors.orange,
                                  ),
                                ),
                                maxLines: 3,
                                onSaved: (value) {
                                  _notes = value ?? '';
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Action Buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: _isSaving
                                ? null
                                : () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isSaving ? null : _saveRecord,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: _isSaving
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
