import 'package:flutter/material.dart';

/// Enum for different module themes
enum DateRangeTheme {
  weight,
  breeding,
  health,
  cattle,
  transaction,
  milk,
  notifications,
  analytics,
}

/// Extension providing theme configuration directly on the enum
extension DateRangeThemeExtension on DateRangeTheme {
  /// Theme color for the module
  Color get color {
    switch (this) {
      case DateRangeTheme.weight:
        return Colors.purple;
      case DateRangeTheme.breeding:
        return Colors.purple;
      case DateRangeTheme.health:
        return Colors.red;
      case DateRangeTheme.cattle:
        return Colors.purple;
      case DateRangeTheme.transaction:
        return Colors.green;
      case DateRangeTheme.milk:
        return Colors.blue;
      case DateRangeTheme.notifications:
        return Colors.orange;
      case DateRangeTheme.analytics:
        return Colors.indigo;
    }
  }

  /// Display name for the module
  String get displayName {
    switch (this) {
      case DateRangeTheme.weight:
        return 'Weight';
      case DateRangeTheme.breeding:
        return 'Breeding';
      case DateRangeTheme.health:
        return 'Health';
      case DateRangeTheme.cattle:
        return 'Cattle';
      case DateRangeTheme.transaction:
        return 'Transaction';
      case DateRangeTheme.milk:
        return 'Milk';
      case DateRangeTheme.notifications:
        return 'Notifications';
      case DateRangeTheme.analytics:
        return 'Analytics';
    }
  }
}
