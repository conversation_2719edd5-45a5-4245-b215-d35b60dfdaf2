import 'package:flutter/material.dart';
import '../controllers/filter_controller.dart';
import '../config/filter_config.dart';
import '../date_range/date_range_themes.dart';
import 'filter_dialog.dart';

/// Universal filter widget following the DateRangeFilterWidget pattern
///
/// This widget provides a clean interface for custom filters with:
/// - Main filter button with active indicator
/// - Dialog for filter selection
/// - Support for dropdown and multi-select chip inputs
/// - Consistent theming across modules
/// - Controller-driven state management
///
/// Usage Example:
/// ```dart
/// FilterWidget(
///   controller: filterController,
///   options: ModuleConfigs.weight.filters,
///   theme: DateRangeTheme.weight,
/// )
/// ```
class FilterWidget extends StatelessWidget {
  final FilterController controller;
  final List<FilterField> options;
  final DateRangeTheme theme;
  final bool compact;
  final EdgeInsets? padding;
  final double? buttonHeight;

  const FilterWidget({
    Key? key,
    required this.controller,
    required this.options,
    required this.theme,
    this.compact = false,
    this.padding,
    this.buttonHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (options.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      color: Colors.white,
      padding: padding ?? EdgeInsets.all(compact ? 4.0 : 8.0),
      child: ListenableBuilder(
        listenable: controller,
        builder: (context, child) {
          final hasActiveFilters = controller.activeFilterValues.isNotEmpty;
          
          return Container(
            height: buttonHeight ?? (compact ? 44 : 48),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: hasActiveFilters ? theme.color : Colors.white,
              border: Border.all(
                color: hasActiveFilters
                    ? theme.color
                    : theme.color.withValues(alpha: 0.3),
                width: hasActiveFilters ? 2 : 1.5,
              ),
              boxShadow: hasActiveFilters
                  ? [
                      BoxShadow(
                        color: theme.color.withValues(alpha: 0.25),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showFilterDialog(context),
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: compact ? 12 : 16,
                    vertical: compact ? 8 : 12,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.tune,
                        size: compact ? 16 : 18,
                        color: hasActiveFilters ? Colors.white : theme.color,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Filters', // Always show "Filters", not the values
                          style: TextStyle(
                            color: hasActiveFilters ? Colors.white : theme.color,
                            fontSize: compact ? 12 : 14,
                            fontWeight: hasActiveFilters ? FontWeight.w600 : FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }



  void _showFilterDialog(BuildContext context) async {
    await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return FilterDialog(
          controller: controller,
          options: options,
          themeColor: theme.color,
        );
      },
    );

    // The dialog handles all state changes internally through the controller
    // No need to handle the result here
  }
}

/// Compact version of FilterWidget for use in tight spaces
class CompactFilterWidget extends StatelessWidget {
  final FilterController controller;
  final List<FilterField> options;
  final DateRangeTheme theme;

  const CompactFilterWidget({
    Key? key,
    required this.controller,
    required this.options,
    required this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FilterWidget(
      controller: controller,
      options: options,
      theme: theme,
      compact: true,
      padding: const EdgeInsets.all(4.0),
      buttonHeight: 36,
    );
  }
}
