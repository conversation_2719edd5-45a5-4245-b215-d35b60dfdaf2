import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/breeding_report_data_isar.dart';
import '../report_tabs/breeding_summary_tab.dart';
import '../report_tabs/breeding_details_tab.dart';

class BreedingReportScreen extends StatefulWidget {
  const BreedingReportScreen({Key? key}) : super(key: key);

  @override
  BreedingReportScreenState createState() => BreedingReportScreenState();
}

class BreedingReportScreenState extends State<BreedingReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  BreedingReportDataIsar? reportData;
  DateTime? startDate;
  DateTime? endDate;
  String searchQuery = '';
  String? selectedStatus;
  String? selectedMethod;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    endDate = DateTime.now();
    startDate = DateTime.now().subtract(const Duration(days: 90));
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Create an empty instance rather than trying to load from DB
      reportData = BreedingReportDataIsar.empty();
      
      // The following code would normally be used to load data:
      // final db = await DatabaseHelper.instance.database;
      // final records = await db.breedingRecordIsars.where().findAll();
      // reportData = BreedingReportDataIsar(
      //   breedingRecords: records,
      //   startDate: startDate,
      //   endDate: endDate,
      //   searchQuery: searchQuery,
      //   selectedStatus: selectedStatus,
      //   selectedMethod: selectedMethod,
      // );

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Breeding Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () {
              // TODO: Implement export functionality
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  BreedingSummaryTab(reportData: reportData!),
                  BreedingDetailsTab(reportData: reportData!),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: startDate != null
                        ? DateFormat('yyyy-MM-dd').format(startDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: endDate != null
                        ? DateFormat('yyyy-MM-dd').format(endDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Status',
                  ),
                  value: selectedStatus,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All Status')),
                    DropdownMenuItem(
                        value: 'Successful', child: Text('Successful')),
                    DropdownMenuItem(value: 'Pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'Failed', child: Text('Failed')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedStatus = value;
                      _loadData();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Method',
                  ),
                  value: selectedMethod,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All Methods')),
                    DropdownMenuItem(value: 'Artificial Insemination', child: Text('AI')),
                    DropdownMenuItem(value: 'Natural Service', child: Text('Natural')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedMethod = value;
                      _loadData();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
