import 'package:flutter/material.dart';
import '../models/cattle_report_data_isar.dart';
import '../report_tabs/cattle_summary_tab.dart';
import '../report_tabs/cattle_details_tab.dart';

class CattleReportScreen extends StatefulWidget {
  const CattleReportScreen({Key? key}) : super(key: key);

  @override
  CattleReportScreenState createState() => CattleReportScreenState();
}

class CattleReportScreenState extends State<CattleReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CattleReportDataIsar reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCategory;
  String? selectedBreed;
  String? selectedStatus;
  bool isLoading = true;
  String? errorMessage;

  final List<String> categories = ['Dairy', 'Beef', 'Calf', 'Heifer', 'Bull'];
  final List<String> statuses = ['Active', 'Inactive', 'Sold', 'Deceased'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initHandlers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initHandlers() async {
    try {
      _loadData();
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to initialize database handlers: $e';
        isLoading = false;
      });
    }
  }

  void _loadData() {
    setState(() {
      isLoading = true;
    });

    try {
      // Create a simplified report data object
      final reportData = CattleReportDataIsar.empty();

      // We would normally populate this with:
      // CattleReportDataIsar(
      //   cattle: cattleData,
      //   healthRecords: healthRecords,
      //   category: selectedCategory,
      //   breed: selectedBreed,
      //   status: selectedStatus,
      // )

      setState(() {
        this.reportData = reportData;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load cattle data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Text(
                  errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  CattleSummaryTab(reportData: reportData),
                  CattleDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedCategory,
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Categories'),
                    ),
                    ...categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: Text(category),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value;
                      _loadData();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedStatus,
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Statuses'),
                    ),
                    ...statuses.map((status) {
                      return DropdownMenuItem<String>(
                        value: status,
                        child: Text(status),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedStatus = value;
                      _loadData();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
