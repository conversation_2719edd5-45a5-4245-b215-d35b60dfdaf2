import 'package:flutter/material.dart';
import '../models/cattle_report_data_isar.dart';

class CattleDetailsTab extends StatelessWidget {
  final CattleReportDataIsar reportData;

  const CattleDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (reportData.totalCattle == null || reportData.totalCattle == 0) {
      return const Center(
        child: Text('No cattle found matching the selected criteria'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: reportData.getTableColumns(),
          rows: reportData.getTableRows(),
        ),
      ),
    );
  }
}
