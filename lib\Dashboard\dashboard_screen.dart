import 'package:flutter/material.dart';
import '../widgets/dashboard_menu_item.dart';
import '../services/database/database_helper.dart';
import 'Farm Setup/models/farm_isar.dart';
import 'Farm Setup/screens/farm_setup_screen.dart';
import 'Cattle/screens/cattle_records_screen.dart';
import 'Reports/screens/reports_screen.dart';
import 'Transactions/screens/transactions_screen.dart';
import 'Milk Records/screens/milk_screen.dart';
import 'Events/screens/events_screen.dart';
import 'Breeding/screens/breeding_screen.dart';
import 'Health/screens/health_screen.dart';
import 'Weight/screens/weight_screen.dart';
import 'widgets/farm_selection_drawer.dart';
import '../constants/app_bar.dart';
import '../constants/app_colors.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  FarmIsar? _selectedFarm;
  bool _isLoading = true;
  final ValueNotifier<FarmIsar?> _farmChangeNotifier = ValueNotifier<FarmIsar?>(null);

  @override
  void initState() {
    super.initState();
    _loadSelectedFarm();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _farmChangeNotifier.dispose();
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _farmChangeNotifier.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadSelectedFarm();
  }

  Future<void> _loadSelectedFarm() async {
    try {
      setState(() => _isLoading = true);
      final farm = await _dbHelper.farmSetupHandler.getActiveFarm();
      setState(() {
        _selectedFarm = farm;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading selected farm: $e');
      setState(() => _isLoading = false);
    }
  }

  void _navigateToScreen(Widget screen) {
    if (!mounted) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => screen),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final selectedFarmName = _selectedFarm?.name ?? 'My Cattle Manager';
    final screenSize = MediaQuery.of(context).size;
    final availableHeight = screenSize.height -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom -
        kToolbarHeight -
        32;

    int crossAxisCount = screenSize.width > 600 ? 3 : 2;

    return Scaffold(
      appBar: AppBarConfig.withDrawer(
        title: selectedFarmName,
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications button tap
            },
          ),
        ],
      ),
      drawer: const FarmSelectionDrawer(),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            const buttonHeight = 48.0;
            final buttonPadding = availableHeight * 0.02;
            final gridHeight =
                constraints.maxHeight - (buttonHeight + buttonPadding * 3);
            final itemWidth =
                (constraints.maxWidth - ((crossAxisCount + 1) * 12)) /
                    crossAxisCount;
            final itemHeight = (gridHeight - 24) / 4;

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: GridView.count(
                        padding: const EdgeInsets.all(12),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: 12.0,
                        crossAxisSpacing: 12.0,
                        childAspectRatio: itemWidth / itemHeight,
                        children: [
                          DashboardMenuItem(
                            title: 'Cattle Records',
                            icon: Icons.pets,
                            color: Colors.blue,
                            onTap: () => _navigateToScreen(const CattleRecordsScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Milk Records',
                            icon: Icons.local_drink,
                            color: Colors.green,
                            onTap: () => _navigateToScreen(const MilkScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Breeding',
                            icon: Icons.favorite,
                            color: Colors.pink,
                            onTap: () => _navigateToScreen(const BreedingScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Health',
                            icon: Icons.medical_services,
                            color: Colors.teal,
                            onTap: () => _navigateToScreen(const HealthScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Weight',
                            icon: Icons.monitor_weight,
                            color: Colors.indigo,
                            onTap: () => _navigateToScreen(const WeightScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Events',
                            icon: Icons.event,
                            color: Colors.orange,
                            onTap: () => _navigateToScreen(const EventsScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Transactions',
                            icon: Icons.account_balance_wallet,
                            color: Colors.purple,
                            onTap: () => _navigateToScreen(const TransactionsScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Reports',
                            icon: Icons.bar_chart,
                            color: Colors.red,
                            onTap: () => _navigateToScreen(const ReportsScreen()),
                          ),
                          DashboardMenuItem(
                            title: 'Farm Setup',
                            icon: Icons.settings,
                            color: Colors.deepPurple,
                            onTap: () => _navigateToScreen(const FarmSetupScreen()),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: buttonHeight,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.sync),
                      label: const Text('Sync Data'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        // Handle sync button tap
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
