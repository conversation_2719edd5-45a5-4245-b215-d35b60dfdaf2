import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_report_data_isar.dart';
import '../report_tabs/milk_summary_tab.dart';
import '../report_tabs/milk_details_tab.dart';
import '../dialogs/export_milk_report_dialog.dart';

class MilkReportScreen extends StatefulWidget {
  const MilkReportScreen({Key? key}) : super(key: key);

  @override
  MilkReportScreenState createState() => MilkReportScreenState();
}

class MilkReportScreenState extends State<MilkReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCattleId;
  double? quantityFilter;
  double? revenueFilter;
  String searchQuery = '';
  bool isLoading = true;
  MilkReportDataIsar? reportData;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Initialize dates to last 30 days
    endDate = DateTime.now();
    startDate = endDate!.subtract(const Duration(days: 30));
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Create an empty report instead of querying the database
      final reportData = MilkReportDataIsar.empty();
      
      // The following code would normally be used to load data:
      // final db = await DatabaseHelper.instance.database;
      // final records = await db.milkRecordIsars.where().findAll();
      // final reportData = MilkReportDataIsar(
      //   allRecords: records,
      //   startDate: startDate,
      //   endDate: endDate,
      //   cattleId: selectedCattleId,
      //   searchQuery: searchQuery,
      // );
      
      setState(() {
        this.reportData = reportData;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text('Failed to load milk records: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _showExportDialog() async {
    await showDialog(
      context: context,
      builder: (context) => ExportMilkReportDialog(reportData: reportData!),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Production Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _showExportDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  MilkSummaryTab(reportData: reportData!),
                  MilkDetailsTab(reportData: reportData!),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Start Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              controller: TextEditingController(
                text: startDate != null
                    ? DateFormat('yyyy-MM-dd').format(startDate!)
                    : '',
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: startDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    startDate = date;
                    _loadData();
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'End Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              controller: TextEditingController(
                text: endDate != null
                    ? DateFormat('yyyy-MM-dd').format(endDate!)
                    : '',
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: endDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    endDate = date;
                    _loadData();
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Cattle ID',
              ),
              value: selectedCattleId,
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('All'),
                ),
                if (!isLoading && reportData != null)
                  ...reportData!.filteredRecords
                      .map((r) => r.cattleId)
                      .toSet()
                      .map((id) => DropdownMenuItem(
                            value: id,
                            child: Text(id),
                          )),
              ],
              onChanged: (value) {
                setState(() {
                  selectedCattleId = value;
                  _loadData();
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
