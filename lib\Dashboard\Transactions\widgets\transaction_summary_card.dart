import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';

/// A widget that displays a summary card for transactions
class TransactionSummaryCard extends StatelessWidget {
  final List<TransactionIsar> transactions;
  final List<CategoryIsar> categories;

  const TransactionSummaryCard({
    Key? key,
    required this.transactions,
    required this.categories,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Card(
      elevation: 6,
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Text('Transaction Summary Card'),
      ),
    );
  }
}
