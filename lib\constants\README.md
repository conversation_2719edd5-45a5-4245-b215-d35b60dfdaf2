# Constants Directory

This directory contains constants and configuration classes used throughout the app.

## AppBar Configuration

The `app_bar.dart` file provides a consistent way to create AppBars throughout the app.

### Usage Examples

#### Standard AppBar
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.standard(
        title: 'My Screen Title',
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Handle settings tap
            },
          ),
        ],
      ),
      body: Container(
        // Your screen content
      ),
    );
  }
}
```

#### AppBar with Drawer
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class MyScreenWithDrawer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withDrawer(
        title: 'Dashboard',
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications
            },
          ),
        ],
      ),
      drawer: MyDrawer(),
      body: Container(
        // Your screen content
      ),
    );
  }
}
```

#### AppBar with Back Button
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class DetailScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Detail Screen',
        context: context,
        actions: [
          AppBarConfig.saveButton(
            onPressed: () {
              // Handle save
            },
          ),
        ],
      ),
      body: Container(
        // Your screen content
      ),
    );
  }
}
```

#### AppBar with Loading Indicator
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class LoadingScreen extends StatelessWidget {
  final bool isSaving = true;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.standard(
        title: 'Saving Data',
        actions: [
          isSaving 
            ? AppBarConfig.loadingIndicator()
            : AppBarConfig.saveButton(
                onPressed: () {
                  // Handle save
                },
              ),
        ],
      ),
      body: Container(
        // Your screen content
      ),
    );
  }
}
``` 