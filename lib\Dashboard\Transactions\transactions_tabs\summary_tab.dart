// Required imports for functionality
// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'dart:async'; // For Timer and Future operations
import 'dart:math' as math; // For mathematical operations
import 'package:flutter/material.dart'; // Core Flutter widgets
import 'package:fl_chart/fl_chart.dart'; // For charts and graphs
import 'package:intl/intl.dart'; // For date and number formatting
import '../models/transaction_isar.dart'; // Transaction data model
// Import Transactions List Tab
import 'package:shared_preferences/shared_preferences.dart'; // For storing currency settings
import '../services/transactions_handler.dart'; // Transactions Handler
import 'package:get_it/get_it.dart'; // For dependency injection
import '../../../utils/message_utils.dart'; // For standardized messages

// For mapIndexed

/// A StatefulWidget that displays a summary of transactions including:
/// - Total cards (Income, Expenses, Net Balance)
/// - Charts (Income/Expense distribution)
/// - Recent transactions
class SummaryTab extends StatefulWidget {
  const SummaryTab({Key? key}) : super(key: key);

  @override
  State<SummaryTab> createState() => _SummaryTabState();
}

/// The state class for TransactionsSummaryTab that manages all the mutable state
/// and UI building logic
class _SummaryTabState extends State<SummaryTab> {
  final TransactionsHandler _transactionsHandler =
      GetIt.instance<TransactionsHandler>();
  List<TransactionIsar> _transactions = [];
  bool _isLoading = true;
  String _selectedPeriod = 'This Month';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // Currency formatter for displaying amounts
  final currencyFormat = NumberFormat.currency(symbol: '\$');

  // Transaction totals
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _netBalance = 0.0;

  // Getters for transaction totals
  double get totalIncome => _totalIncome;
  double get totalExpenses => _totalExpenses;
  double get netBalance => _netBalance;

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  // Add this to the class variables
  String _selectedChartType = 'Income';

  @override
  void initState() {
    super.initState();
    _loadCurrencySettings();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final transactions =
          await _transactionsHandler.getTransactionsForDateRange(
        _startDate,
        _endDate,
      );

      if (!mounted) return;

      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
      _calculateTotals();
    } catch (e) {
      debugPrint('Error loading transactions: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        FinancialMessageUtils.showError(context, 'Error loading transactions');
      }
    }
  }

  Future<void> _loadCurrencySettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _currencySymbol = prefs.getString('currencySymbol') ?? '\$';
      _symbolBeforeAmount = prefs.getBool('symbolBeforeAmount') ?? true;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Updates the transaction totals
  void _calculateTotals() {
    _totalIncome = _transactions
        .where((transaction) => transaction.categoryType == 'Income')
        .fold(0.0, (sum, transaction) => sum + transaction.amount);

    _totalExpenses = _transactions
        .where((transaction) => transaction.categoryType == 'Expense')
        .fold(0.0, (sum, transaction) => sum + transaction.amount);

    _netBalance = _totalIncome - _totalExpenses;
  }

  void _onPeriodChanged(String? newPeriod) {
    if (newPeriod == null) return;

    setState(() {
      _selectedPeriod = newPeriod;
      switch (newPeriod) {
        case 'This Week':
          _startDate = DateTime.now().subtract(const Duration(days: 7));
          _endDate = DateTime.now();
          break;
        case 'This Month':
          _startDate = DateTime.now().subtract(const Duration(days: 30));
          _endDate = DateTime.now();
          break;
        case 'This Year':
          _startDate = DateTime.now().subtract(const Duration(days: 365));
          _endDate = DateTime.now();
          break;
        case 'All Time':
          _startDate = DateTime(2000);
          _endDate = DateTime.now();
          break;
      }
    });
    _loadData();
  }

  /// Builds the total cards row
  Widget _buildSummaryCards() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth > 600;
        final cardWidth = isWideScreen
            ? (constraints.maxWidth - 32) / 3 // 32 is total spacing (16 * 2)
            : (constraints.maxWidth - 24) / 3; // 24 is total spacing (8 * 3)

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSummaryCard(
              'Total Income',
              _totalIncome,
              Icons.arrow_upward,
              Colors.green,
              cardWidth,
              isWideScreen,
            ),
            SizedBox(width: isWideScreen ? 16 : 8),
            _buildSummaryCard(
              'Total Expenses',
              _totalExpenses,
              Icons.arrow_downward,
              Colors.red,
              cardWidth,
              isWideScreen,
            ),
            SizedBox(width: isWideScreen ? 16 : 8),
            _buildSummaryCard(
              'Net Balance',
              _netBalance,
              Icons.account_balance_wallet,
              Colors.blue,
              cardWidth,
              isWideScreen,
            ),
          ],
        );
      },
    );
  }

  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    MaterialColor color,
    double width,
    bool isWideScreen,
  ) {
    final height = isWideScreen ? width * 0.8 : width * 1.2;

    return SizedBox(
      width: width,
      height: height,
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: EdgeInsets.all(isWideScreen ? 16 : 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color.shade700, size: isWideScreen ? 32 : 24),
              SizedBox(height: isWideScreen ? 16 : 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: isWideScreen ? 16 : 12,
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: isWideScreen ? 8 : 4),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  _symbolBeforeAmount
                      ? '$_currencySymbol${amount.toStringAsFixed(2)}'
                      : '${amount.toStringAsFixed(2)}$_currencySymbol',
                  style: TextStyle(
                    fontSize: isWideScreen ? 24 : 18,
                    fontWeight: FontWeight.w800,
                    color: title == 'Net Balance'
                        ? (amount >= 0
                            ? Colors.green.shade700
                            : Colors.red.shade700)
                        : color.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the chart type buttons
  Widget _buildChartTypeButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: _buildChartTypeButton(
              'Income',
              Icons.arrow_upward,
              Colors.green[700]!,
              Colors.green[50]!,
              _selectedChartType == 'Income',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: _buildChartTypeButton(
              'Expense',
              Icons.arrow_downward,
              Colors.red[700]!,
              Colors.red[50]!,
              _selectedChartType == 'Expense',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 4,
            child: _buildChartTypeButton(
              'Net Balance',
              Icons.account_balance_wallet,
              Colors.blue[700]!,
              Colors.blue[50]!,
              _selectedChartType == 'Net Balance',
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a chart type button
  Widget _buildChartTypeButton(
    String text,
    IconData icon,
    Color color,
    Color backgroundColor,
    bool isSelected,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => setState(() => _selectedChartType = text),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? color : backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: color,
              width: 1.5,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : color,
                size: 14,
              ),
              const SizedBox(width: 3),
              Text(
                text,
                style: TextStyle(
                  color: isSelected ? Colors.white : color,
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a pie chart
  Widget _buildPieChart(List<PieChartSectionData> sections, String title) {
    if (sections.isEmpty) {
      return Card(
        elevation: 6,
        margin: const EdgeInsets.all(16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'No data available',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8.0),
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16), // Reduced from 48
            LayoutBuilder(
              builder: (context, constraints) {
                final availableWidth = constraints.maxWidth;
                final chartSize = math.min(availableWidth * 0.6, 350.0);

                return Column(
                  children: [
                    SizedBox(
                      height: chartSize + 150,
                      child: Center(
                        child: SizedBox(
                          width: chartSize,
                          height: chartSize,
                          child: PieChart(
                            PieChartData(
                              sections: sections
                                  .map(
                                    (section) => PieChartSectionData(
                                      color: section.color,
                                      value: section.value,
                                      title: section.title,
                                      radius: 85,
                                      titleStyle: section.titleStyle,
                                      badgeWidget: null,
                                      badgePositionPercentageOffset: 1.4,
                                    ),
                                  )
                                  .toList(),
                              centerSpaceRadius: 30,
                              sectionsSpace: 3,
                              centerSpaceColor: Colors.white,
                              borderData: FlBorderData(show: false),
                              startDegreeOffset: 180,
                              pieTouchData: PieTouchData(
                                touchCallback:
                                    (FlTouchEvent event, pieTouchResponse) {
                                  // Optional: Add interaction handling
                                },
                              ),
                            ),
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOutQuart,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16), // Reduced from 48
                    Wrap(
                      spacing: 24,
                      runSpacing: 16,
                      alignment: WrapAlignment.center,
                      children: _buildLegend(sections),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the legend for a pie chart
  List<Widget> _buildLegend(List<PieChartSectionData> sections) {
    // If sections are empty or null, return an empty list
    if (sections.isEmpty) return [];

    // For net balance distribution, create a custom legend
    if (sections.length == 2) {
      // Calculate total for percentage and amount details
      double total = sections.fold(0, (sum, section) => sum + section.value);

      return [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      color: Colors.green[700]!,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Total Income',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        Text(
                          '${(sections[0].value / total * 100).toStringAsFixed(1)}% | ${_symbolBeforeAmount ? '$_currencySymbol${sections[0].value.toStringAsFixed(2)}' : '${sections[0].value.toStringAsFixed(2)}$_currencySymbol'}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      color: Colors.red[700]!,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Total Expense',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                        Text(
                          '${(sections[1].value / total * 100).toStringAsFixed(1)}% | ${_symbolBeforeAmount ? '$_currencySymbol${sections[1].value.toStringAsFixed(2)}' : '${sections[1].value.toStringAsFixed(2)}$_currencySymbol'}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        )
      ];
    }

    // For income and expense distributions, use category-based legend
    return [
      Wrap(
        spacing: 16,
        runSpacing: 8,
        children: sections.map((section) {
          // Extract category name from the badge widget
          String category = 'Unknown';
          if (section.badgeWidget is Text) {
            category = (section.badgeWidget as Text).data ?? 'Unknown';
          }

          // Calculate percentage
          double total = sections.fold(0, (sum, s) => sum + s.value);
          double percentage = (section.value / total) * 100;

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                color: section.color,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category,
                    style: TextStyle(
                      color: section.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${percentage.toStringAsFixed(1)}% | ${_symbolBeforeAmount ? '$_currencySymbol${section.value.toStringAsFixed(2)}' : '${section.value.toStringAsFixed(2)}$_currencySymbol'}',
                    style: TextStyle(
                      color: section.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          );
        }).toList(),
      )
    ];
  }

  /// Gets the income sections for the pie chart
  List<PieChartSectionData> _getIncomeSections() {
    final incomeByCategory = <String, double>{};
    for (var transaction in _transactions) {
      if (transaction.categoryType == 'Income') {
        incomeByCategory[transaction.category] =
            (incomeByCategory[transaction.category] ?? 0) + transaction.amount;
      }
    }

    if (incomeByCategory.isEmpty) {
      return [
        PieChartSectionData(
          color: Colors.grey[300]!,
          value: 1,
          title: 'No Data',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ];
    }

    double total =
        incomeByCategory.values.fold(0, (sum, amount) => sum + amount);

    return incomeByCategory.entries.map((entry) {
      final amount = entry.value;
      final percentage = (amount / total) * 100;
      Color color = _getColors()[entry.key.hashCode % _getColors().length];

      return PieChartSectionData(
        color: color,
        value: amount,
        title: '${percentage.toStringAsFixed(1)}%',
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        radius: 50,
        badgeWidget: Text(entry.key),
        badgePositionPercentageOffset: 1.2,
      );
    }).toList();
  }

  /// Gets the expense sections for the pie chart
  List<PieChartSectionData> _getExpenseSections() {
    final expenseByCategory = <String, double>{};
    for (var transaction in _transactions) {
      if (transaction.categoryType == 'Expense') {
        expenseByCategory[transaction.category] =
            (expenseByCategory[transaction.category] ?? 0) + transaction.amount;
      }
    }

    if (expenseByCategory.isEmpty) {
      return [
        PieChartSectionData(
          color: Colors.grey[300]!,
          value: 1,
          title: 'No Data',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ];
    }

    double total =
        expenseByCategory.values.fold(0, (sum, amount) => sum + amount);

    return expenseByCategory.entries.map((entry) {
      final amount = entry.value;
      final percentage = (amount / total) * 100;
      Color color = _getColors()[entry.key.hashCode % _getColors().length];

      return PieChartSectionData(
        color: color,
        value: amount,
        title: '${percentage.toStringAsFixed(1)}%',
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        radius: 50,
        badgeWidget: Text(entry.key),
        badgePositionPercentageOffset: 1.2,
      );
    }).toList();
  }

  /// Gets the net balance sections for the pie chart
  List<PieChartSectionData> _getNetBalanceSections() {
    double totalIncome = 0;
    double totalExpense = 0;

    for (var transaction in _transactions) {
      if (transaction.categoryType == 'Income') {
        totalIncome += transaction.amount;
      } else if (transaction.categoryType == 'Expense') {
        totalExpense += transaction.amount;
      }
    }

    List<PieChartSectionData> sections = [];

    if (totalIncome == 0 && totalExpense == 0) {
      return [
        PieChartSectionData(
          color: Colors.grey[300]!,
          value: 1,
          title: 'No Data',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ];
    }

    sections.add(
      PieChartSectionData(
        color: Colors.green[700]!,
        value: totalIncome,
        title:
            '${(totalIncome / (totalIncome + totalExpense) * 100).toStringAsFixed(1)}%',
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        radius: 50,
        badgeWidget: Text(
          'Income',
          style: TextStyle(
            color: Colors.green[700]!,
            fontWeight: FontWeight.bold,
          ),
        ),
        badgePositionPercentageOffset: 1.2,
      ),
    );

    sections.add(
      PieChartSectionData(
        color: Colors.red[700]!,
        value: totalExpense,
        title:
            '${(totalExpense / (totalIncome + totalExpense) * 100).toStringAsFixed(1)}%',
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        radius: 50,
        badgeWidget: Text(
          'Expense',
          style: TextStyle(
            color: Colors.red[700]!,
            fontWeight: FontWeight.bold,
          ),
        ),
        badgePositionPercentageOffset: 1.2,
      ),
    );

    return sections;
  }

  List<Color> _getColors() {
    return [
      Colors.blue[700]!,
      Colors.red[700]!,
      Colors.green[700]!,
      Colors.purple[700]!,
      Colors.orange[700]!,
      Colors.teal[700]!,
      Colors.indigo[700]!,
      Colors.deepOrange[700]!,
      Colors.cyan[700]!,
      Colors.brown[700]!,
      Colors.deepPurple[700]!,
      Colors.lightBlue[700]!,
      Colors.lightGreen[700]!,
      Colors.pink[700]!,
      Colors.amber[700]!,
    ];
  }

  // Utility Methods
  /// Builds the loading or error state
  Widget _buildLoadingOrErrorState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// Gets the expenses by category
  Map<String, double> get expensesByCategory {
    final map = <String, double>{};
    for (var transaction in _transactions) {
      if (transaction.categoryType == 'Expense') {
        map[transaction.category] =
            (map[transaction.category] ?? 0) + transaction.amount;
      }
    }
    return map;
  }

  /// Builds the main layout of the summary tab
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingOrErrorState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Period selector
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Select Period',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButton<String>(
                    value: _selectedPeriod,
                    isExpanded: true,
                    items: ['This Week', 'This Month', 'This Year', 'All Time']
                        .map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                    onChanged: _onPeriodChanged,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Summary cards
          _buildSummaryCards(),
          const SizedBox(height: 16),

          // Charts
          _buildCharts(),
        ],
      ),
    );
  }

  Widget _buildCharts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildChartTypeButtons(),
        const SizedBox(height: 16),
        if (_selectedChartType == 'Income')
          _buildPieChart(_getIncomeSections(), 'Income Distribution')
        else if (_selectedChartType == 'Expense')
          _buildPieChart(_getExpenseSections(), 'Expense Distribution')
        else if (_selectedChartType == 'Net Balance')
          _buildPieChart(_getNetBalanceSections(), 'Net Balance Distribution'),
      ],
    );
  }
}

class DailyData {
  final DateTime date;
  final double income;
  final double expenses;
  final double net;

  DailyData({
    required this.date,
    required this.income,
    required this.expenses,
    required this.net,
  });
}
