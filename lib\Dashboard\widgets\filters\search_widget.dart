import 'dart:async';
import 'package:flutter/material.dart';
import 'controllers/filter_controller.dart';
import 'date_range/date_range_themes.dart';

/// Configuration class for SearchWidget UI parameters
class SearchWidgetConfig {
  final bool compact;
  final EdgeInsets? padding;
  final double? buttonHeight;
  final Duration debounceDelay;

  const SearchWidgetConfig({
    this.compact = false,
    this.padding,
    this.buttonHeight,
    this.debounceDelay = const Duration(milliseconds: 500),
  });

  /// Standard configuration for normal use
  static const SearchWidgetConfig standard = SearchWidgetConfig();

  /// Compact configuration for tight spaces
  static const SearchWidgetConfig compactConfig = SearchWidgetConfig(
    compact: true,
    padding: EdgeInsets.all(4.0),
    buttonHeight: 36,
  );

  /// Dense configuration for very tight spaces
  static const SearchWidgetConfig denseConfig = SearchWidgetConfig(
    compact: true,
    padding: EdgeInsets.all(2.0),
    buttonHeight: 32,
    debounceDelay: Duration(milliseconds: 300),
  );
}

/// Universal search widget with debounced input following the DateRangeFilterWidget pattern
///
/// This widget provides a clean search interface with:
/// - Debounced input to prevent excessive API calls
/// - Clear button functionality
/// - Consistent theming across modules
/// - Controller-driven state management
/// - Proper TextEditingController lifecycle management
///
/// Usage Example:
/// ```dart
/// SearchWidget(
///   controller: filterController,
///   hintText: 'Search by cattle name or tag...',
///   theme: DateRangeTheme.weight,
///   config: SearchWidgetConfig.standard,
/// )
/// ```
class SearchWidget extends StatefulWidget {
  final FilterController controller;
  final String hintText;
  final DateRangeTheme theme;
  final SearchWidgetConfig config;

  const SearchWidget({
    Key? key,
    required this.controller,
    required this.hintText,
    required this.theme,
    this.config = SearchWidgetConfig.standard,
  }) : super(key: key);

  /// Convenience constructor for compact search widget
  const SearchWidget.compact({
    Key? key,
    required FilterController controller,
    required String hintText,
    required DateRangeTheme theme,
  }) : this(
         key: key,
         controller: controller,
         hintText: hintText,
         theme: theme,
         config: SearchWidgetConfig.compactConfig,
       );

  /// Convenience constructor for dense search widget
  const SearchWidget.dense({
    Key? key,
    required FilterController controller,
    required String hintText,
    required DateRangeTheme theme,
  }) : this(
         key: key,
         controller: controller,
         hintText: hintText,
         theme: theme,
         config: SearchWidgetConfig.denseConfig,
       );

  @override
  State<SearchWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  late TextEditingController _textController;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.controller.searchQuery);

    // Add listener for instantaneous UI updates
    _textController.addListener(() {
      setState(() {}); // Trigger rebuild for visual changes
    });
  }

  @override
  void didUpdateWidget(SearchWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle controller changes
    if (oldWidget.controller != widget.controller) {
      _textController.text = widget.controller.searchQuery;
    }

    // Sync text if controller's search query changed externally (e.g., "clear all")
    if (_textController.text != widget.controller.searchQuery) {
      _textController.text = widget.controller.searchQuery;
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _textController.dispose();
    super.dispose();
  }

  void _onTextChanged(String text) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Start new timer
    _debounceTimer = Timer(widget.config.debounceDelay, () {
      if (mounted) {
        widget.controller.setSearchQuery(text);
      }
    });
  }

  void _clearSearch() {
    _textController.clear();
    _debounceTimer?.cancel();
    widget.controller.setSearchQuery('');
  }

  @override
  Widget build(BuildContext context) {
    final hasText = _textController.text.isNotEmpty;

    return Container(
      color: Colors.white,
      padding: widget.config.padding ?? EdgeInsets.all(widget.config.compact ? 4.0 : 8.0),
      child: Container(
        height: widget.config.buttonHeight ?? (widget.config.compact ? 44 : 50),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
          border: Border.all(
            color: hasText
                ? widget.theme.color.withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.25),
            width: hasText ? 2 : 1.5,
          ),
          boxShadow: hasText
              ? [
                  BoxShadow(
                    color: widget.theme.color.withValues(alpha: 0.15),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.12),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          children: [
            // Enhanced search icon
            Padding(
              padding: const EdgeInsets.only(left: 18, right: 12),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: hasText
                      ? widget.theme.color.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.search,
                  size: widget.config.compact ? 18 : 20,
                  color: hasText ? widget.theme.color : Colors.grey[600],
                ),
              ),
            ),

            // Enhanced text field
            Expanded(
              child: TextField(
                controller: _textController,
                onChanged: _onTextChanged,
                style: TextStyle(
                  fontSize: widget.config.compact ? 14 : 15,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                ),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: widget.config.compact ? 14 : 15,
                    fontWeight: FontWeight.normal,
                    letterSpacing: 0.2,
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    vertical: widget.config.compact ? 14 : 16,
                  ),
                ),
              ),
            ),

            // Enhanced clear button
            if (hasText)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _clearSearch,
                    borderRadius: BorderRadius.circular(24),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: widget.theme.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: widget.theme.color.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.close,
                        size: 16,
                        color: widget.theme.color,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}


