import 'filter_config.dart';
import '../../../Farm Setup/services/farm_setup_handler.dart';
import '../../../Farm Setup/models/animal_type_isar.dart';
import '../../../Cattle/services/cattle_handler.dart';
import 'package:get_it/get_it.dart';

/// Real data service for dynamic filter options using farm setup data
class FilterDataService {
  static final _farmSetupHandler = GetIt.instance<FarmSetupHandler>();
  static final _cattleHandler = CattleHandler.instance;

  /// Fetch animal types from the farm setup database
  static Future<List<String>> getAnimalTypes(Map<String, dynamic>? dependencies) async {
    try {
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      return animalTypes.map((type) => type.name ?? '').where((name) => name.isNotEmpty).toList();
    } catch (e) {
      // Fallback to default animal types if database fails
      return ['Cow', 'Buffalo', 'Goat', 'Sheep', 'Horse'];
    }
  }

  /// Fetch breeds based on selected animal type from the farm setup database
  static Future<List<String>> getBreeds(Map<String, dynamic>? dependencies) async {
    try {
      final animalTypeName = dependencies?['animalType'] as String?;

      // If no animal type is specified, return all breeds from all animal types
      if (animalTypeName == null || animalTypeName.isEmpty) {
        final allBreeds = <String>{};
        final animalTypes = await _farmSetupHandler.getAllAnimalTypes();

        for (final animalType in animalTypes) {
          if (animalType.businessId != null) {
            final breeds = await _farmSetupHandler.getBreedCategoriesForAnimalType(animalType.businessId!);
            allBreeds.addAll(breeds.map((breed) => breed.name ?? '').where((name) => name.isNotEmpty));
          }
        }

        return allBreeds.toList()..sort();
      }

      // First, find the animal type ID by name
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.name?.toLowerCase() == animalTypeName.toLowerCase(),
        orElse: () => AnimalTypeIsar(),
      );

      if (animalType.businessId == null) return [];

      // Get breeds for this animal type
      final breeds = await _farmSetupHandler.getBreedCategoriesForAnimalType(animalType.businessId!);
      return breeds.map((breed) => breed.name ?? '').where((name) => name.isNotEmpty).toList();
    } catch (e) {
      // Fallback to predefined breeds based on animal type
      final animalTypeName = dependencies?['animalType'] as String?;

      if (animalTypeName == null || animalTypeName.isEmpty) {
        // Return all breeds from all animal types
        return [
          'Holstein', 'Angus', 'Jersey', 'Hereford', 'Simmental', // Cow breeds
          'Murrah', 'Nili-Ravi', 'Surti', 'Jaffarabadi', // Buffalo breeds
          'Alpine', 'Boer', 'Nubian', 'Saanen', 'LaMancha', // Goat breeds
          'Merino', 'Suffolk', 'Dorper', 'Romney', 'Corriedale', // Sheep breeds
          'Arabian', 'Quarter Horse', 'Thoroughbred', 'Clydesdale', // Horse breeds
        ]..sort();
      }

      switch (animalTypeName.toLowerCase()) {
        case 'cow':
        case 'cattle':
          return ['Holstein', 'Angus', 'Jersey', 'Hereford', 'Simmental'];
        case 'buffalo':
          return ['Murrah', 'Nili-Ravi', 'Surti', 'Jaffarabadi'];
        case 'goat':
          return ['Alpine', 'Boer', 'Nubian', 'Saanen', 'LaMancha'];
        case 'sheep':
          return ['Merino', 'Suffolk', 'Dorper', 'Romney', 'Corriedale'];
        case 'horse':
          return ['Arabian', 'Quarter Horse', 'Thoroughbred', 'Clydesdale'];
        default:
          return [];
      }
    }
  }

  /// Fetch cattle based on animal type, breed, and gender from the cattle database
  /// Returns cattle in format "Name (TagID)"
  static Future<List<String>> getCattle(Map<String, dynamic>? dependencies) async {
    try {
      final breed = dependencies?['breed'] as String?;
      final gender = dependencies?['gender'] as String?;

      // Get cattle from the database using CattleHandler
      var cattle = await _cattleHandler.getAllCattle();

      // Apply filters in memory for simplicity
      if (breed != null) {
        cattle = cattle.where((c) => c.breedId == breed).toList();
      }
      if (gender != null) {
        cattle = cattle.where((c) => c.gender == gender).toList();
      }

      // Limit results for performance
      if (cattle.length > 50) {
        cattle = cattle.take(50).toList();
      }

      // Format cattle as "Name (TagID)"
      return cattle.map((cattle) {
        final name = cattle.name ?? 'Unknown';
        final tagId = cattle.tagId ?? cattle.businessId ?? 'No Tag';
        return '$name ($tagId)';
      }).toList();
    } catch (e) {
      // Fallback to simulated cattle names with tag IDs
      final animalType = dependencies?['animalType'] as String?;
      final breed = dependencies?['breed'] as String?;
      final gender = dependencies?['gender'] as String?;

      if (animalType == null) return [];

      final List<String> cattle = [];
      for (int i = 1; i <= 10; i++) {
        final genderPrefix = gender == 'Male' ? 'Bull' : gender == 'Female' ? 'Cow' : 'Animal';
        final name = breed != null ? '$genderPrefix $breed $i' : '$genderPrefix $i';
        final tagId = 'TAG${i.toString().padLeft(3, '0')}';
        cattle.add('$name ($tagId)');
      }
      return cattle;
    }
  }

  /// Generate weight ranges based on animal type
  static Future<List<String>> getWeightRanges(Map<String, dynamic>? dependencies) async {
    final animalType = dependencies?['animalType'] as String?;

    switch (animalType?.toLowerCase()) {
      case 'cow':
      case 'cattle':
      case 'buffalo':
        return ['Under 200kg', '200-400kg', '400-600kg', '600-800kg', 'Over 800kg'];
      case 'sheep':
        return ['Under 30kg', '30-50kg', '50-70kg', '70-90kg', 'Over 90kg'];
      case 'goat':
        return ['Under 25kg', '25-40kg', '40-60kg', '60-80kg', 'Over 80kg'];
      case 'horse':
        return ['Under 300kg', '300-450kg', '450-600kg', '600-750kg', 'Over 750kg'];
      default:
        return ['Under 100kg', '100-200kg', '200-300kg', '300-400kg', 'Over 400kg'];
    }
  }

  /// Get breeding statuses from the system
  static Future<List<String>> getBreedingStatuses(Map<String, dynamic>? dependencies) async {
    return ['Open', 'Bred', 'Pregnant', 'Fresh', 'Dry'];
  }

  /// Get breeding methods from the system
  static Future<List<String>> getBreedingMethods(Map<String, dynamic>? dependencies) async {
    return ['Natural', 'AI', 'ET', 'IVF'];
  }

  /// Get health statuses from the system
  static Future<List<String>> getHealthStatuses(Map<String, dynamic>? dependencies) async {
    return ['Healthy', 'Sick', 'Under Treatment', 'Quarantine', 'Recovering'];
  }
}

/// Central registry for module-specific filter configurations
///
/// This class provides pre-defined filter configurations for each module
/// in the application, following the established theming pattern.
class ModuleConfigs {
  ModuleConfigs._(); // Private constructor to prevent instantiation



  /// Dynamic weight module filter configuration with dependencies
  static const ModuleFilterConfig weightDynamic = ModuleFilterConfig(
    filters: [
      // Core filters in preferred order: Cattle, Animal Type, Breed, Gender
      FilterField.dynamicDropdown(
        key: 'cattle',
        label: 'Cattle',
        optionsLoader: FilterDataService.getCattle,
        placeholder: 'Select cattle',
      ),
      FilterField.dynamicDropdown(
        key: 'animalType',
        label: 'Animal Type',
        optionsLoader: FilterDataService.getAnimalTypes,
        placeholder: 'Select animal type',
      ),
      FilterField.dynamicDropdown(
        key: 'breed',
        label: 'Breed',
        optionsLoader: FilterDataService.getBreeds,
        placeholder: 'Select breed',
      ),
      FilterField.dropdown(
        key: 'gender',
        label: 'Gender',
        options: ['Male', 'Female'],
        placeholder: 'Select gender',
      ),
      // Module-specific filters
      FilterField.dynamicDropdown(
        key: 'weightRange',
        label: 'Weight Range',
        optionsLoader: FilterDataService.getWeightRanges,
        placeholder: 'Select weight range',
      ),
      FilterField.dropdown(
        key: 'measurementMethod',
        label: 'Measurement Method',
        options: ['Scale', 'Tape Measure', 'Visual Estimate', 'Weight Tape'],
        placeholder: 'Select method',
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'weight', label: 'Weight'),
      SortField(key: 'tagId', label: 'Tag ID'),
    ],
    searchHint: 'Search by cattle name or tag...',
  );

  /// Dynamic breeding module filter configuration
  static const ModuleFilterConfig breedingDynamic = ModuleFilterConfig(
    filters: [
      // Core filters
      FilterField.dynamicDropdown(
        key: 'animalType',
        label: 'Animal Type',
        optionsLoader: FilterDataService.getAnimalTypes,
        placeholder: 'Select animal type',
      ),
      FilterField.dropdown(
        key: 'gender',
        label: 'Gender',
        options: ['Male', 'Female'],
        placeholder: 'Select gender',
      ),
      FilterField.dynamicDropdown(
        key: 'breed',
        label: 'Breed',
        optionsLoader: FilterDataService.getBreeds,
        dependsOn: ['animalType'],
        placeholder: 'Select breed',
      ),
      FilterField.dynamicDropdown(
        key: 'cattle',
        label: 'Cattle',
        optionsLoader: FilterDataService.getCattle,
        dependsOn: ['animalType', 'breed', 'gender'],
        placeholder: 'Select cattle',
      ),
      // Module-specific filters
      FilterField.dynamicMultiSelectChip(
        key: 'breedingStatus',
        label: 'Breeding Status',
        optionsLoader: FilterDataService.getBreedingStatuses,
      ),
      FilterField.dynamicDropdown(
        key: 'breedingMethod',
        label: 'Breeding Method',
        optionsLoader: FilterDataService.getBreedingMethods,
        placeholder: 'Select method',
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'expectedDate', label: 'Expected Date'),
      SortField(key: 'cattleName', label: 'Cattle Name'),
      SortField(key: 'breed', label: 'Breed'),
      SortField(key: 'breedingStatus', label: 'Status'),
    ],
    searchHint: 'Search by cattle name, tag, or bull...',
  );

  /// Dynamic health module filter configuration
  static const ModuleFilterConfig healthDynamic = ModuleFilterConfig(
    filters: [
      // Core filters
      FilterField.dynamicDropdown(
        key: 'animalType',
        label: 'Animal Type',
        optionsLoader: FilterDataService.getAnimalTypes,
        placeholder: 'Select animal type',
      ),
      FilterField.dropdown(
        key: 'gender',
        label: 'Gender',
        options: ['Male', 'Female'],
        placeholder: 'Select gender',
      ),
      FilterField.dynamicDropdown(
        key: 'breed',
        label: 'Breed',
        optionsLoader: FilterDataService.getBreeds,
        dependsOn: ['animalType'],
        placeholder: 'Select breed',
      ),
      FilterField.dynamicDropdown(
        key: 'cattle',
        label: 'Cattle',
        optionsLoader: FilterDataService.getCattle,
        dependsOn: ['animalType', 'breed', 'gender'],
        placeholder: 'Select cattle',
      ),
      // Module-specific filters
      FilterField.dynamicMultiSelectChip(
        key: 'healthStatus',
        label: 'Health Status',
        optionsLoader: FilterDataService.getHealthStatuses,
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'cattleName', label: 'Cattle Name'),
      SortField(key: 'breed', label: 'Breed'),
      SortField(key: 'healthStatus', label: 'Health Status'),
      SortField(key: 'treatmentType', label: 'Treatment Type'),
    ],
    searchHint: 'Search by cattle name, tag, or treatment...',
  );

  /// Breeding module filter configuration
  static const ModuleFilterConfig breeding = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'breedingStatus',
        label: 'Breeding Status',
        options: ['Pregnant', 'Open', 'Bred', 'Calved'],
        placeholder: 'Select status',
      ),
      FilterField.dropdown(
        key: 'breedingMethod',
        label: 'Breeding Method',
        options: ['AI', 'Natural', 'ET'],
        placeholder: 'Select method',
      ),
      FilterField.multiSelectChip(
        key: 'pregnancyStage',
        label: 'Pregnancy Stage',
        options: [
          'First Trimester',
          'Second Trimester',
          'Third Trimester',
          'Due Soon'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'breedingDate', label: 'Breeding Date'),
      SortField(key: 'dueDate', label: 'Due Date'),
      SortField(key: 'cattleName', label: 'Cattle Name'),
      SortField(key: 'status', label: 'Status'),
    ],
    searchHint: 'Search by cattle name or breeding details...',
  );

  /// Health module filter configuration
  static const ModuleFilterConfig health = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'healthStatus',
        label: 'Health Status',
        options: ['Healthy', 'Sick', 'Under Treatment', 'Quarantine'],
        placeholder: 'Select status',
      ),
      FilterField.dropdown(
        key: 'treatmentType',
        label: 'Treatment Type',
        options: ['Vaccination', 'Medication', 'Surgery', 'Preventive'],
        placeholder: 'Select treatment',
      ),
      FilterField.multiSelectChip(
        key: 'symptoms',
        label: 'Symptoms',
        options: [
          'Fever',
          'Loss of Appetite',
          'Lameness',
          'Respiratory Issues',
          'Digestive Issues'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'treatmentDate', label: 'Treatment Date'),
      SortField(key: 'nextCheckup', label: 'Next Checkup'),
      SortField(key: 'cattleName', label: 'Cattle Name'),
      SortField(key: 'severity', label: 'Severity'),
    ],
    searchHint: 'Search by cattle name or health condition...',
  );

  /// Cattle module filter configuration
  static const ModuleFilterConfig cattle = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'gender',
        label: 'Gender',
        options: ['Male', 'Female'],
        placeholder: 'Select gender',
      ),
      FilterField.dropdown(
        key: 'breed',
        label: 'Breed',
        options: [
          'Holstein',
          'Jersey',
          'Angus',
          'Hereford',
          'Simmental',
          'Charolais',
          'Limousin',
          'Other'
        ],
        placeholder: 'Select breed',
      ),
      FilterField.dropdown(
        key: 'status',
        label: 'Status',
        options: ['Active', 'Sold', 'Deceased', 'Transferred'],
        placeholder: 'Select status',
      ),
      FilterField.multiSelectChip(
        key: 'ageGroup',
        label: 'Age Group',
        options: [
          'Calf (0-1 year)',
          'Heifer (1-2 years)',
          'Young Adult (2-3 years)',
          'Adult (3+ years)'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'name', label: 'Name'),
      SortField(key: 'tagNumber', label: 'Tag Number'),
      SortField(key: 'birthDate', label: 'Birth Date'),
      SortField(key: 'breed', label: 'Breed'),
      SortField(key: 'weight', label: 'Weight'),
    ],
    searchHint: 'Search by name, tag number, or breed...',
  );

  /// Transaction module filter configuration
  static const ModuleFilterConfig transaction = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'transactionType',
        label: 'Transaction Type',
        options: ['Income', 'Expense'],
        placeholder: 'Select type',
      ),
      FilterField.dropdown(
        key: 'category',
        label: 'Category',
        options: [
          'Feed',
          'Medical',
          'Equipment',
          'Labor',
          'Sales',
          'Other'
        ],
        placeholder: 'Select category',
      ),
      FilterField.multiSelectChip(
        key: 'amountRange',
        label: 'Amount Range',
        options: [
          'Under \$100',
          '\$100-\$500',
          '\$500-\$1000',
          '\$1000-\$5000',
          'Over \$5000'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'amount', label: 'Amount'),
      SortField(key: 'category', label: 'Category'),
      SortField(key: 'description', label: 'Description'),
    ],
    searchHint: 'Search by description or category...',
  );

  /// Milk records module filter configuration
  static const ModuleFilterConfig milk = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'milkingSession',
        label: 'Milking Session',
        options: ['Morning', 'Evening', 'Both'],
        placeholder: 'Select session',
      ),
      FilterField.multiSelectChip(
        key: 'productionRange',
        label: 'Production Range',
        options: [
          'Under 10L',
          '10-20L',
          '20-30L',
          '30-40L',
          'Over 40L'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'quantity', label: 'Quantity'),
      SortField(key: 'cattleName', label: 'Cattle Name'),
      SortField(key: 'session', label: 'Session'),
    ],
    searchHint: 'Search by cattle name...',
  );

  /// Notifications module filter configuration
  static const ModuleFilterConfig notifications = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'priority',
        label: 'Priority',
        options: ['High', 'Medium', 'Low'],
        placeholder: 'Select priority',
      ),
      FilterField.dropdown(
        key: 'status',
        label: 'Status',
        options: ['Unread', 'Read', 'Archived'],
        placeholder: 'Select status',
      ),
      FilterField.multiSelectChip(
        key: 'type',
        label: 'Notification Type',
        options: [
          'Health Alert',
          'Breeding Reminder',
          'Weight Update',
          'Transaction',
          'System'
        ],
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'priority', label: 'Priority'),
      SortField(key: 'type', label: 'Type'),
    ],
    searchHint: 'Search notifications...',
  );

  /// Analytics module filter configuration (minimal filters for summary views)
  static const ModuleFilterConfig analytics = ModuleFilterConfig(
    filters: [
      FilterField.dropdown(
        key: 'reportType',
        label: 'Report Type',
        options: ['Weight', 'Breeding', 'Health', 'Financial', 'Production'],
        placeholder: 'Select report type',
      ),
    ],
    sorts: [
      SortField(key: 'date', label: 'Date'),
      SortField(key: 'value', label: 'Value'),
    ],
    searchHint: 'Search reports...',
    enableSearch: false, // Analytics typically don't need search
  );

  /// Get configuration for a specific module by name
  static ModuleFilterConfig? getConfig(String moduleName) {
    switch (moduleName.toLowerCase()) {
      case 'weight':
        return weightDynamic; // Use dynamic configuration with proper data fetching
      case 'breeding':
        return breedingDynamic; // Use dynamic configuration
      case 'health':
        return healthDynamic; // Use dynamic configuration
      case 'cattle':
        return cattle;
      case 'transaction':
      case 'transactions':
        return transaction;
      case 'milk':
      case 'milkrecords':
        return milk;
      case 'notifications':
        return notifications;
      case 'analytics':
        return analytics;
      default:
        return null;
    }
  }

  /// Get all available module names
  static List<String> get availableModules => [
        'weight',
        'breeding',
        'health',
        'cattle',
        'transaction',
        'milk',
        'notifications',
        'analytics',
      ];
}
