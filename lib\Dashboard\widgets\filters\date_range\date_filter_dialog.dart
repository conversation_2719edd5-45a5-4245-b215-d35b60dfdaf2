import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'date_range_constants.dart';

/// Completely self-sufficient dialog widget for date range selection
///
/// This dialog only accepts data (current dates and theme) and handles all
/// logic internally. It returns the selected date range via Navigator.pop().
class DateFilterDialog extends StatefulWidget {
  final DateTime? currentStartDate;
  final DateTime? currentEndDate;
  final Color themeColor;

  const DateFilterDialog({
    Key? key,
    this.currentStartDate,
    this.currentEndDate,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<DateFilterDialog> createState() => _DateFilterDialogState();
}

class _DateFilterDialogState extends State<DateFilterDialog> {
  late DateTime _localStartDate;
  late DateTime _localEndDate;
  bool _showCustomRange = false;

  @override
  void initState() {
    super.initState();
    // Initialize with current dates only if they exist, otherwise no default selection
    _localStartDate = widget.currentStartDate ?? DateTime.now();
    _localEndDate = widget.currentEndDate ?? DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isCompact = screenSize.width < 600;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.9,
          maxHeight: screenSize.height * 0.7, // Reduced from 0.85 to 0.7
          minWidth: 300,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCompactHeader(context, isCompact),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isCompact ? 12 : 16),
                child: _showCustomRange
                    ? _buildCompactCustomRangeContent(context, isCompact)
                    : _buildCompactQuickFilterContent(context, isCompact),
              ),
            ),
            _buildCompactActions(context, isCompact),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactHeader(BuildContext context, bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.themeColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Icon(
              _showCustomRange ? Icons.date_range : Icons.calendar_today,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _showCustomRange ? 'Custom Range' : 'Date Range Filter',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (_showCustomRange)
            IconButton(
              onPressed: () {
                setState(() {
                  _showCustomRange = false;
                });
              },
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            )
          else
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close, color: Colors.white),
            ),
        ],
      ),
    );
  }

  Widget _buildCompactQuickFilterContent(BuildContext context, bool isCompact) {
    final crossAxisCount = isCompact ? 2 : 3;

    return Column(
        children: [
          // Grid layout for quick filters
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: isCompact ? 2.2 : 2.5,
            ),
            itemCount: DateRangePreset.values.where((preset) => preset.isFixedDays).length,
            itemBuilder: (context, index) {
              final preset = DateRangePreset.values
                  .where((preset) => preset.isFixedDays)
                  .elementAt(index);
              return _buildCompactQuickFilterOption(preset, isCompact);
            },
          ),
          SizedBox(height: isCompact ? 12 : 16),
          // Custom Range Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showCustomRange = true;
                });
              },
              icon: Icon(Icons.date_range, color: widget.themeColor, size: isCompact ? 18 : 20),
              label: Text(
                DateRangePreset.custom.label,
                style: TextStyle(
                  color: widget.themeColor,
                  fontSize: isCompact ? 14 : 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: widget.themeColor, width: 1.5),
                padding: EdgeInsets.symmetric(vertical: isCompact ? 12 : 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
    );
  }

  Widget _buildCompactQuickFilterOption(DateRangePreset preset, bool isCompact) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    DateTime startDate;
    DateTime endDate;

    if (preset.days == 0) {
      // Today
      startDate = today;
      endDate = today;
    } else if (preset.days == 1) {
      // Yesterday
      final yesterday = today.subtract(const Duration(days: 1));
      startDate = yesterday;
      endDate = yesterday;
    } else {
      // Last X days
      startDate = today.subtract(Duration(days: preset.days!));
      endDate = today;
    }

    final isSelected = _isDateRangeSelected(startDate, endDate, preset.days!);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            _localStartDate = startDate;
            _localEndDate = endDate;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? widget.themeColor : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? widget.themeColor : widget.themeColor.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1.5,
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: widget.themeColor.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Center(
            child: Text(
              preset.label,
              style: TextStyle(
                color: isSelected ? Colors.white : widget.themeColor,
                fontSize: isCompact ? 12 : 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  // Keep the old method for backward compatibility
  Widget _buildQuickFilterOption(DateRangePreset preset) {
    return _buildCompactQuickFilterOption(preset, false);
  }

  Widget _buildCompactCustomRangeContent(BuildContext context, bool isCompact) {
    return Column(
        children: [
          _buildCompactDateSelector(
            'Start Date',
            _localStartDate,
            (date) => setState(() => _localStartDate = date),
            isCompact,
          ),
          SizedBox(height: isCompact ? 12 : 16),
          _buildCompactDateSelector(
            'End Date',
            _localEndDate,
            (date) => setState(() => _localEndDate = date),
            isCompact,
          ),
        ],
    );
  }

  // Keep old method for backward compatibility
  Widget _buildCustomRangeContent() {
    return _buildCompactCustomRangeContent(context, false);
  }

  Widget _buildCompactDateSelector(String label, DateTime date, Function(DateTime) onDateChanged, bool isCompact) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          final selectedDate = await showDatePicker(
            context: context,
            initialDate: date,
            firstDate: DateTime(2020),
            lastDate: DateTime.now().add(const Duration(days: 365)),
            builder: (context, child) {
              return Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: Theme.of(context).colorScheme.copyWith(
                    primary: widget.themeColor,
                  ),
                ),
                child: child!,
              );
            },
          );

          if (selectedDate != null) {
            onDateChanged(selectedDate);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.themeColor.withValues(alpha: 0.3),
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: widget.themeColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calendar_today,
                  color: widget.themeColor,
                  size: isCompact ? 16 : 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: isCompact ? 11 : 12,
                        color: widget.themeColor.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      DateFormat('MMM dd, yyyy').format(date),
                      style: TextStyle(
                        fontSize: isCompact ? 14 : 16,
                        color: widget.themeColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_drop_down,
                color: widget.themeColor.withValues(alpha: 0.7),
                size: isCompact ? 20 : 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Keep old method for backward compatibility
  Widget _buildDateSelector(String label, DateTime date, Function(DateTime) onDateChanged) {
    return _buildCompactDateSelector(label, date, onDateChanged, false);
  }

  Widget _buildCompactActions(BuildContext context, bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop({
                  'startDate': _localStartDate,
                  'endDate': _localEndDate,
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Apply Filter',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Keep old method for backward compatibility
  Widget _buildActions() {
    return _buildCompactActions(context, false);
  }

  bool _isDateRangeSelected(DateTime startDate, DateTime endDate, int days) {
    return _isSameDay(_localStartDate, startDate) && _isSameDay(_localEndDate, endDate);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
