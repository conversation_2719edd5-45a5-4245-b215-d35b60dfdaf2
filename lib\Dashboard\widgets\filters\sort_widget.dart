import 'package:flutter/material.dart';
import 'controllers/filter_controller.dart';
import 'config/filter_config.dart';
import 'date_range/date_range_themes.dart';

/// Universal sort widget following the DateRangeFilterWidget pattern
///
/// This widget provides a clean interface for sorting with:
/// - Dropdown for sort field selection
/// - Toggle button for sort direction
/// - Consistent theming across modules
/// - Controller-driven state management
///
/// Usage Example:
/// ```dart
/// SortWidget(
///   controller: filterController,
///   options: ModuleConfigs.weight.sorts,
///   theme: DateRangeTheme.weight,
/// )
/// ```
class SortWidget extends StatelessWidget {
  final FilterController controller;
  final List<SortField> options;
  final DateRangeTheme theme;
  final bool compact;
  final EdgeInsets? padding;
  final double? buttonHeight;

  const SortWidget({
    Key? key,
    required this.controller,
    required this.options,
    required this.theme,
    this.compact = false,
    this.padding,
    this.buttonHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (options.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      color: Colors.white,
      padding: padding ?? EdgeInsets.all(compact ? 4.0 : 8.0),
      child: ListenableBuilder(
        listenable: controller,
        builder: (context, child) {
          final hasActiveSort = controller.sortBy != null;
          
          return Container(
            height: buttonHeight ?? (compact ? 44 : 48),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: hasActiveSort ? theme.color : Colors.white,
              border: Border.all(
                color: hasActiveSort
                    ? theme.color
                    : theme.color.withValues(alpha: 0.3),
                width: hasActiveSort ? 2 : 1.5,
              ),
              boxShadow: hasActiveSort
                  ? [
                      BoxShadow(
                        color: theme.color.withValues(alpha: 0.25),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showSortDialog(context),
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: compact ? 12 : 16,
                    vertical: compact ? 8 : 12,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.sort,
                        size: compact ? 16 : 18,
                        color: hasActiveSort ? Colors.white : theme.color,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Sort', // Always show "Sort", not the values
                          style: TextStyle(
                            color: hasActiveSort ? Colors.white : theme.color,
                            fontSize: compact ? 12 : 14,
                            fontWeight: hasActiveSort ? FontWeight.w600 : FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }



  void _showSortDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _CompactSortDialog(
          controller: controller,
          options: options,
          themeColor: theme.color,
        );
      },
    );
  }
}

/// Compact version of SortWidget for use in tight spaces
class CompactSortWidget extends StatelessWidget {
  final FilterController controller;
  final List<SortField> options;
  final DateRangeTheme theme;

  const CompactSortWidget({
    Key? key,
    required this.controller,
    required this.options,
    required this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SortWidget(
      controller: controller,
      options: options,
      theme: theme,
      compact: true,
      padding: const EdgeInsets.all(4.0),
      buttonHeight: 36,
    );
  }
}

/// Modern, compact sort dialog with responsive design
class _CompactSortDialog extends StatefulWidget {
  final FilterController controller;
  final List<SortField> options;
  final Color themeColor;

  const _CompactSortDialog({
    Key? key,
    required this.controller,
    required this.options,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<_CompactSortDialog> createState() => _CompactSortDialogState();
}

class _CompactSortDialogState extends State<_CompactSortDialog> {
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isCompact = screenSize.width < 600;
    const animationDuration = Duration(milliseconds: 200);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: AnimatedContainer(
        duration: animationDuration,
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.9,
          maxHeight: screenSize.height * 0.7,
          minWidth: 300,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(isCompact),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isCompact ? 12 : 16),
                child: Column(
                  children: [
                    _buildSortOptions(isCompact),
                    if (widget.controller.sortBy != null)
                      _buildSortDirection(isCompact),
                  ],
                ),
              ),
            ),
            _buildActions(isCompact),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.themeColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.sort,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Sort Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions(bool isCompact) {
    return Column(
      children: widget.options.map((sortField) {
        final isSelected = widget.controller.sortBy == sortField.key;
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  widget.controller.setSort(sortField.key);
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: EdgeInsets.all(isCompact ? 12 : 16),
                decoration: BoxDecoration(
                  color: isSelected ? widget.themeColor.withValues(alpha: 0.1) : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? widget.themeColor : Colors.grey.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: isSelected ? widget.themeColor : Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _getSortIcon(sortField.key),
                        color: isSelected ? Colors.white : widget.themeColor,
                        size: isCompact ? 16 : 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            sortField.label,
                            style: TextStyle(
                              color: isSelected ? widget.themeColor : Colors.black87,
                              fontSize: isCompact ? 14 : 15,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            ),
                          ),
                          if (isSelected)
                            Text(
                              widget.controller.isAscending ? _getAscendingText(sortField.key) : _getDescendingText(sortField.key),
                              style: TextStyle(
                                color: widget.themeColor.withValues(alpha: 0.7),
                                fontSize: isCompact ? 11 : 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSortDirection(bool isCompact) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isCompact ? 12 : 16),
      padding: EdgeInsets.all(isCompact ? 12 : 16),
      decoration: BoxDecoration(
        color: widget.themeColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.themeColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: widget.themeColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              widget.controller.isAscending ? Icons.arrow_upward : Icons.arrow_downward,
              color: widget.themeColor,
              size: isCompact ? 16 : 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.controller.isAscending ? 'Ascending Order' : 'Descending Order',
              style: TextStyle(
                color: widget.themeColor,
                fontSize: isCompact ? 14 : 15,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Switch(
            value: widget.controller.isAscending,
            onChanged: (value) {
              setState(() {
                widget.controller.toggleSortDirection();
              });
            },
            activeColor: widget.themeColor,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildActions(bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (widget.controller.sortBy != null)
            Expanded(
              child: TextButton(
                onPressed: () {
                  widget.controller.setSort(null);
                  Navigator.of(context).pop();
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Clear Sort'),
              ),
            ),
          if (widget.controller.sortBy != null)
            const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Apply Sort',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get appropriate icon for each sort field
  IconData _getSortIcon(String sortKey) {
    switch (sortKey.toLowerCase()) {
      case 'date':
        return Icons.calendar_today;
      case 'weight':
        return Icons.scale;
      case 'cattlename':
      case 'cattle_name':
      case 'name':
        return Icons.pets;
      case 'tagid':
      case 'tag_id':
        return Icons.tag;
      case 'breed':
        return Icons.category;
      case 'animaltype':
      case 'animal_type':
        return Icons.pets;
      case 'gender':
        return Icons.wc;
      case 'age':
        return Icons.cake;
      case 'status':
        return Icons.info;
      case 'location':
        return Icons.location_on;
      case 'price':
      case 'amount':
      case 'cost':
        return Icons.attach_money;
      default:
        return Icons.sort;
    }
  }

  /// Get ascending text for each sort field
  String _getAscendingText(String sortKey) {
    switch (sortKey.toLowerCase()) {
      case 'date':
        return 'Oldest first';
      case 'weight':
        return 'Lightest first';
      case 'cattlename':
      case 'cattle_name':
      case 'name':
      case 'tagid':
      case 'tag_id':
      case 'breed':
        return 'A to Z';
      case 'age':
        return 'Youngest first';
      case 'price':
      case 'amount':
      case 'cost':
        return 'Lowest first';
      default:
        return 'Ascending';
    }
  }

  /// Get descending text for each sort field
  String _getDescendingText(String sortKey) {
    switch (sortKey.toLowerCase()) {
      case 'date':
        return 'Newest first';
      case 'weight':
        return 'Heaviest first';
      case 'cattlename':
      case 'cattle_name':
      case 'name':
      case 'tagid':
      case 'tag_id':
      case 'breed':
        return 'Z to A';
      case 'age':
        return 'Oldest first';
      case 'price':
      case 'amount':
      case 'cost':
        return 'Highest first';
      default:
        return 'Descending';
    }
  }
}
