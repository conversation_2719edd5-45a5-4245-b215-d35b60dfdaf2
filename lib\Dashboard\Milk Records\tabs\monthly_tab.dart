import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../widgets/empty_state.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

class MonthlyTab extends StatefulWidget {
  final List<MilkRecordIsar> filteredRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final DateTime? selectedMonth;
  final Function(BuildContext, List<DateTime>) selectMonth;
  final Function(DateTime?) onMonthChanged;

  const MonthlyTab({
    Key? key,
    required this.filteredRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    this.selectedMonth,
    required this.selectMonth,
    required this.onMonthChanged,
  }) : super(key: key);

  @override
  State<MonthlyTab> createState() => _MonthlyTabState();
}

class _MonthlyTabState extends State<MonthlyTab> {
  DateTime? _selectedMonth;

  // Milk tab color palette - matching the main milk tab colors
  static const _primaryColor = Color(0xFF6A1B9A); // Purple (Monthly tab color)
  static const _secondaryColor = Color(0xFF2E7D32); // Green (Daily)
  static const _accentColor = Color(0xFF1976D2); // Blue (Weekly)
  static const _redColor = Color(0xFFD32F2F); // Red (Total)
  static const _morningColor = Color(0xFF1976D2); // Blue for morning
  static const _eveningColor = Color(0xFF7B1FA2); // Purple for evening
  static const _cardShadowColor = Color(0x1A000000);
  static const _cardBorderRadius = 12.0; // Match milk tab border radius

  // Animation duration
  static const _animDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _selectedMonth = widget.selectedMonth;
  }

  @override
  void didUpdateWidget(MonthlyTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedMonth != oldWidget.selectedMonth) {
      _selectedMonth = widget.selectedMonth;
    }
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
    Color iconColor = Colors.white,
    Color textColor = Colors.black87,
    Color valueColor = Colors.black87,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and icon row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 4,
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor.withAlpha(204), // 0.8 * 255 = 204
                    fontWeight: FontWeight.w700,
                    fontSize: 12,
                    letterSpacing: 0.5,
                  ),
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(38), // 0.15 * 255 = 38
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: iconColor, size: 16),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Value with large font
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w900,
              color: valueColor,
              letterSpacing: -0.5,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: textColor.withAlpha(179), // 0.7 * 255 = 179
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCattleListItem(
    CattleIsar? cattle,
    double value,
    double percentage, {
    bool showTrend = true,
    Color color = Colors.blue,
  }) {
    final greyColor = Colors.grey.shade600;
    final cattleName = cattle?.name ?? 'Unknown';
    final tagId = cattle?.tagId ?? '';
    final displayName =
        (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                cattle?.animalTypeId == 'cow' ? Icons.emoji_nature : Icons.pets,
                color: color.withAlpha(179), // 0.7 * 255 = 179
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: Text(
                  displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  '${value.toStringAsFixed(1)} L',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.shade200,
                    color: color,
                    minHeight: 5,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (showTrend)
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: percentage > 20 ? Colors.green : greyColor,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardWithHeader(
    String title,
    IconData icon,
    Widget content, {
    Color headerColor = Colors.black87,
  }) {
    return Card(
      elevation: 4,
      shadowColor: _cardShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cardBorderRadius),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: headerColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(_cardBorderRadius),
                topRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: headerColor, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: headerColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(_cardBorderRadius),
                bottomRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildMonthCard(
      DateTime month, List<MilkRecordIsar> monthRecords, List<DateTime> months) {
    // Group records by month
    final recordsByMonth = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        final month = DateTime(record.date!.year, record.date!.month, 1);
        recordsByMonth.putIfAbsent(month, () => []).add(record);
      }
    }

    // Calculate monthly totals
    double totalProduction = 0;
    double morningTotal = 0;
    double eveningTotal = 0;
    final dailyProduction = <DateTime, double>{};
    final cattleTotals = <String, double>{};
    
    for (var record in monthRecords) {
      totalProduction += record.totalYield;
      morningTotal += record.morningAmount ?? 0;
      eveningTotal += record.eveningAmount ?? 0;
      
      // Track by day for chart
      if (record.date != null) {
        final day = DateTime(
          record.date!.year, 
          record.date!.month,
          record.date!.day
        );
        dailyProduction[day] = (dailyProduction[day] ?? 0) + record.totalYield;
      }
      
      // Track by cattle
      final tagId = record.cattleTagId ?? '';
      if (tagId.isNotEmpty) {
        cattleTotals[tagId] = (cattleTotals[tagId] ?? 0) + record.totalYield;
      }
    }

    // Calculate average per cattle
    final avgPerCattle = cattleTotals.isNotEmpty
        ? totalProduction / cattleTotals.length
        : 0;

    // Calculate month-over-month change
    double monthlyChange = 0;
    final previousMonthDate = DateTime(month.year, month.month - 1, 1);
    final previousMonthRecords = widget.filteredRecords
        .where((record) =>
            record.date?.year == previousMonthDate.year &&
            record.date?.month == previousMonthDate.month)
        .toList();

    if (previousMonthRecords.isNotEmpty) {
      final prevMonthTotal = previousMonthRecords.fold(
          0.0,
          (sum, record) =>
              sum + record.morningAmount! + record.eveningAmount!);
      if (prevMonthTotal > 0) {
        monthlyChange =
            ((totalProduction - prevMonthTotal) / prevMonthTotal) * 100;
      }
    }

    // Get top cattle producers
    final sortedCattle = cattleTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top5Cattle = sortedCattle.take(5).toList();

    // Morning/Evening percentages
    final morningPercentage =
        totalProduction > 0 ? (morningTotal / totalProduction * 100) : 0;
    final eveningPercentage =
        totalProduction > 0 ? (eveningTotal / totalProduction * 100) : 0;

    return AnimatedContainer(
      duration: _animDuration,
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with month selection
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(_cardBorderRadius),
              boxShadow: const [
                BoxShadow(
                  color: _cardShadowColor,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      DateFormat('MMMM yyyy').format(month),
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.calendar_month,
                          color: _primaryColor),
                      onPressed: () => widget.selectMonth(context, months),
                      style: IconButton.styleFrom(
                        backgroundColor:
                            _primaryColor.withAlpha(26), // 0.1 * 255 = 26
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${monthRecords.length} Records',
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Key Stats Section
          _buildCardWithHeader(
            'Key Production Metrics',
            Icons.analytics,
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.local_drink,
                        iconColor: _primaryColor,
                        valueColor: _primaryColor,
                        title: 'TOTAL PRODUCTION',
                        value: '${totalProduction.toStringAsFixed(1)} L',
                        subtitle: 'Combined milk yield',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _secondaryColor,
                        valueColor: _secondaryColor,
                        title: 'AVERAGE DAILY',
                        value:
                            '${(totalProduction / DateTime(month.year, month.month + 1, 0).day).toStringAsFixed(1)} L',
                        subtitle: 'Per day average',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _accentColor,
                        valueColor: _accentColor,
                        title: 'AVERAGE PER COW',
                        value: '${avgPerCattle.toStringAsFixed(1)} L',
                        subtitle: '${cattleTotals.keys.length} cattle',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.star,
                        iconColor: _secondaryColor, // Green for top producer
                        valueColor: _secondaryColor, // Green for top producer
                        title: 'TOP PRODUCER',
                        value: top5Cattle.isNotEmpty
                            ? '${top5Cattle.first.value.toStringAsFixed(1)} L'
                            : 'N/A',
                        subtitle: top5Cattle.isNotEmpty
                            ? (() {
                                final cattle =
                                    widget.cattleMap[top5Cattle.first.key];
                                final cattleName = cattle?.name ?? 'Unknown';
                                final tagId = cattle?.tagId ?? '';
                                return (tagId.isNotEmpty)
                                    ? '$cattleName ($tagId)'
                                    : cattleName;
                              })()
                            : '',
                      ),
                    ),
                  ],
                ),
                if (monthlyChange != 0) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: monthlyChange > 0
                          ? _secondaryColor.withAlpha(26) // Green with alpha
                          : _redColor.withAlpha(26), // Red with alpha
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          monthlyChange > 0
                              ? Icons.trending_up
                              : Icons.trending_down,
                          color: monthlyChange > 0 ? _secondaryColor : _redColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${monthlyChange.abs().toStringAsFixed(1)}% ${monthlyChange > 0 ? 'increase' : 'decrease'} from previous month',
                            style: TextStyle(
                              color:
                                  monthlyChange > 0 ? Colors.green : Colors.red,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            headerColor: _primaryColor,
          ),
          const SizedBox(height: 16),

          // Morning/Evening Distribution Card
          _buildCardWithHeader(
            'Production Distribution',
            Icons.pie_chart_rounded, // Match milk tab icon style
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.wb_sunny_rounded, color: _morningColor, size: 16), // Match milk tab icon
                          SizedBox(width: 4),
                          Text(
                            'Morning',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _morningColor,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${morningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _morningColor,
                        ),
                      ),
                      Text(
                        '(${morningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color:
                              _morningColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.nightlight_rounded,
                              color: _eveningColor, size: 16), // Match milk tab icon
                          SizedBox(width: 4),
                          Text(
                            'Evening',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _eveningColor,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${eveningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _eveningColor,
                        ),
                      ),
                      Text(
                        '(${eveningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color:
                              _eveningColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            headerColor: _secondaryColor,
          ),
          const SizedBox(height: 16),

          // Top Performers Card
          _buildCardWithHeader(
            'Top Producers',
            Icons.trending_up,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...top5Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    color: _secondaryColor, // Green for top producers
                  );
                }).toList(),
              ],
            ),
            headerColor: _secondaryColor, // Green for top producers
          ),
          const SizedBox(height: 16),

          // Low Producers Card
          _buildCardWithHeader(
            'Low Producers',
            Icons.trending_down,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...sortedCattle.reversed.take(3).map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    color: _redColor, // Red for low producers (avoiding orange)
                  );
                }).toList(),
              ],
            ),
            headerColor: _redColor, // Red for low producers (avoiding orange)
          ),
          const SizedBox(height: 16),

          // Monthly Production Alerts Card
          if (sortedCattle.isNotEmpty)
            _buildCardWithHeader(
              'Production Alerts',
              Icons.warning_amber_rounded,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.info_outline, color: _redColor, size: 18),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Cattle producing below 80% of monthly average',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...sortedCattle
                      .where((entry) => entry.value < avgPerCattle * 0.8)
                      .take(3)
                      .map((entry) {
                    final cattle = widget.cattleMap[entry.key];
                    final percentage = (entry.value / avgPerCattle * 100);
                    return _buildCattleListItem(
                      cattle,
                      entry.value,
                      percentage,
                      showTrend: false,
                      color: _redColor,
                    );
                  }).toList(),
                  if (sortedCattle
                      .where((entry) => entry.value < avgPerCattle * 0.8)
                      .isEmpty)
                    const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text(
                        'No cattle are significantly underperforming this month.',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                ],
              ),
              headerColor: _redColor,
            ),
          const SizedBox(height: 16),

          // Weekly production breakdown with alerts
          _buildCardWithHeader(
            'Weekly Production Breakdown',
            Icons.bar_chart,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Production Alerts
                if (dailyProduction.entries
                    .any((e) => e.value < totalProduction * 0.8)) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.warning_amber_rounded,
                                color: _redColor, size: 18),
                            SizedBox(width: 8),
                            Text(
                              'Production Alerts',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: _redColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...dailyProduction.entries
                            .where((e) => e.value < totalProduction * 0.8)
                            .map((entry) {
                          final dropPercentage =
                              ((totalProduction - entry.value) /
                                  totalProduction *
                                  100);
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.arrow_downward,
                                  color: _redColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Day ${entry.key.day}: ${entry.value.toStringAsFixed(1)} L '
                                    '(${dropPercentage.toStringAsFixed(1)}% below average)',
                                    style: const TextStyle(color: _redColor),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ],

                // Weekly average label
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: _accentColor.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                            color:
                                _accentColor.withAlpha(76)), // 0.3 * 255 = 76
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.horizontal_rule,
                            color: _accentColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Monthly Average: ${totalProduction.toStringAsFixed(1)} L',
                            style: const TextStyle(
                              color: _accentColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(
                  height: 220,
                  child: BarChart(
                    BarChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: true,
                        horizontalInterval: 5,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: Colors.grey.shade200,
                            strokeWidth: 1,
                          );
                        },
                        getDrawingVerticalLine: (value) {
                          return FlLine(
                            color: Colors.grey.shade200,
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 40,
                            getTitlesWidget: (value, meta) {
                              return Text(
                                value.toInt().toString(),
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                              );
                            },
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              final day = value.toInt();
                              return Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  'Day $day',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                      minY: 0,
                      barTouchData: BarTouchData(
                        enabled: true,
                        handleBuiltInTouches: true,
                        touchTooltipData: BarTouchTooltipData(
                          getTooltipItem: (group, groupIndex, rod, rodIndex) {
                            final day = group.x + 1;
                            return BarTooltipItem(
                              'Day $day: ${rod.toY.toStringAsFixed(1)} L',
                              const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      ),
                      barGroups: dailyProduction.entries.map((entry) {
                        return BarChartGroupData(
                          x: entry.key.day - 1,
                          barRods: [
                            BarChartRodData(
                              toY: entry.value,
                              color: _primaryColor,
                              width: 16,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(4),
                              ),
                              backDrawRodData: BackgroundBarChartRodData(
                                show: true,
                                toY: totalProduction * 1.2,
                                color: Colors.grey.shade200,
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
            headerColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filteredRecords.isEmpty) {
      return MilkEmptyState(
        icon: Icons.filter_alt_off_outlined,
        message: 'No Records Found',
        subtitle: 'No records found for the selected filters',
        color: _primaryColor,
        action: ElevatedButton.icon(
          onPressed: () {
            setState(() {
              _selectedMonth = null;
              widget.onMonthChanged(null);
            });
          },
          icon: const Icon(Icons.refresh),
          label: const Text('Show All Months'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      );
    }

    // Group records by month
    final recordsByMonth = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        final month = DateTime(record.date!.year, record.date!.month, 1);
        recordsByMonth.putIfAbsent(month, () => []).add(record);
      }
    }

    // Sort months in descending order
    final months = recordsByMonth.keys.toList()..sort((a, b) => b.compareTo(a));

    // If a month is selected, show only that month
    if (_selectedMonth != null) {
      final selectedMonth =
          DateTime(_selectedMonth!.year, _selectedMonth!.month, 1);

      if (recordsByMonth.containsKey(selectedMonth)) {
        return SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMonthCard(
                    selectedMonth, recordsByMonth[selectedMonth]!, months),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedMonth = null;
                        widget.onMonthChanged(null);
                      });
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Show All Months'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // If the selected month doesn't have records, show a message
        return MilkEmptyState(
          icon: Icons.calendar_month_outlined,
          message: 'No Records for Selected Month',
          subtitle: 'No records found for the selected month',
          color: _primaryColor,
          action: ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedMonth = null;
                widget.onMonthChanged(null);
              });
            },
            icon: const Icon(Icons.list),
            label: const Text('Show All Months'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      }
    }

    return SingleChildScrollView(
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...months.map((month) {
              final monthRecords = recordsByMonth[month]!;
              return _buildMonthCard(month, monthRecords, months);
            }).toList(),
          ],
        ),
      ),
    );
  }
}
