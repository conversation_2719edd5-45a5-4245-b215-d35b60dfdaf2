import 'package:flutter/material.dart';
import '../models/weight_record_isar.dart';
import '../controllers/weight_controller.dart';
import '../services/weight_service.dart';
import '../../widgets/index.dart';
import '../widgets/weight_record_card.dart';
import '../dialogs/weight_form_dialog.dart';
import '../details/cattle_weight_detail_screen.dart';
// NEW: Import the universal filter system
import '../../widgets/filters/index.dart';

class WeightRecordsTab extends StatefulWidget {
  final WeightController controller;

  const WeightRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightRecordsTab> createState() => _WeightRecordsTabState();
}

class _WeightRecordsTabState extends State<WeightRecordsTab>
    with AutomaticKeepAliveClientMixin {

  // NEW: Universal filter controller replaces individual filter state
  late FilterController _filterController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize the universal filter controller with theme
    _filterController = FilterController(theme: DateRangeTheme.weight);

    // Listen to filter changes and sync with weight controller
    _filterController.addListener(_onFiltersChanged);

    // Sync initial state from weight controller
    _syncInitialState();
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Sync initial filter state from weight controller
  void _syncInitialState() {
    if (widget.controller.startDate != null || widget.controller.endDate != null) {
      _filterController.setDateRange(
        widget.controller.startDate,
        widget.controller.endDate,
      );
    }

    if (widget.controller.searchQuery.isNotEmpty) {
      _filterController.setSearchQuery(widget.controller.searchQuery);
    }
  }

  /// Handle filter changes and update weight controller
  void _onFiltersChanged() {
    // Update weight controller with new filter values
    widget.controller.setDateRange(
      _filterController.startDate,
      _filterController.endDate,
    );

    widget.controller.setSearchQuery(_filterController.searchQuery);

    // TODO: Extend WeightController to support additional filters
    // For now, we'll just handle the basic filters that already exist
  }



  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        return Column(
          children: [
            // NEW: Pattern 1 - Full Filter System
            // Single line replaces entire old filter section
            FullFilterLayout(
              controller: _filterController,
              config: ModuleConfigs.getConfig('weight')!,
              totalCount: widget.controller.allWeightRecords.length,
              filteredCount: widget.controller.filteredWeightRecords.length,
            ),

            // Records list remains the same
            Expanded(
              child: _buildRecordsList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecordsList() {
    final filteredRecords = widget.controller.filteredWeightRecords;
    final allRecords = widget.controller.allWeightRecords;

    if (filteredRecords.isEmpty) {
      return EmptyState.custom(
        icon: Icons.monitor_weight,
        title: 'No Weight Records',
        message: allRecords.isEmpty
            ? 'Start by adding your first weight record.'
            : 'No records match your current filters.',
        action: allRecords.isEmpty ? null : ElevatedButton(
          onPressed: () {
            // NEW: Use filter controller's clear method
            _filterController.clearAll();
          },
          child: const Text('Clear Filters'),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => widget.controller.refresh(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: filteredRecords.length,
        itemBuilder: (context, index) {
          final record = filteredRecords[index];
          final cattle = record.cattleBusinessId != null
              ? widget.controller.getCattle(record.cattleBusinessId!)
              : null;

          return WeightRecordCard(
            record: record,
            cattle: cattle,
            isSelected: false, // Simplified - no selection mode for now
            isSelectionMode: false,
            onTap: () => _onRecordTap(record),
            onLongPress: () => _onRecordLongPress(record),
            onSelectionChanged: (selected) => {}, // Simplified
            onEdit: () => _editWeightRecord(record),
            onDelete: () => _deleteWeightRecord(record),
          );
        },
      ),
    );
  }

  void _onRecordTap(WeightRecordIsar record) {
    _navigateToCattleWeightDetail(record);
  }

  void _onRecordLongPress(WeightRecordIsar record) {
    // Simplified - no selection mode for now
  }

  void _navigateToCattleWeightDetail(WeightRecordIsar record) {
    if (record.cattleBusinessId == null) return;

    final cattle = widget.controller.getCattle(record.cattleBusinessId!);
    if (cattle != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleWeightDetailScreen(
            cattle: cattle,
            onRefresh: () => widget.controller.refresh(),
          ),
        ),
      );
    }
  }

  void _editWeightRecord(WeightRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: widget.controller.allCattle,
        existingRecord: record,
        onRecordAdded: () => widget.controller.refresh(),
      ),
    );
  }

  void _deleteWeightRecord(WeightRecordIsar record) async {
    final cattle = record.cattleBusinessId != null
        ? widget.controller.getCattle(record.cattleBusinessId!)
        : null;
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Record'),
        content: Text(
          'Are you sure you want to delete the weight record for $cattleName (${record.weight.toStringAsFixed(1)} kg)?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Use the weight service from the controller
        final weightService = WeightService();
        await weightService.deleteWeightRecord(record.businessId!);
        await widget.controller.refresh();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Weight record deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting record: $e')),
          );
        }
      }
    }
  }
}