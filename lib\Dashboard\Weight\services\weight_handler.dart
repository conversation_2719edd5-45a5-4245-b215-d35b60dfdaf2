import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';

import '../models/weight_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../services/streams/stream_service.dart';

/// Consolidated handler for all Weight module database operations
class WeightHandler {
  static final Logger _logger = Logger('WeightHandler');
  final IsarService _isarService;
  final Uuid _uuid = const Uuid();

  // Singleton instance
  static final WeightHandler _instance = WeightHandler._internal();
  static WeightHandler get instance => _instance;

  // Private constructor
  WeightHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== WEIGHT RECORDS ===//

  /// Get all weight records
  Future<List<WeightRecordIsar>> getAllWeightRecords() async {
    try {
      return await _isar.weightRecordIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error getting all weight records: $e');
      throw DatabaseException('Failed to retrieve weight records', e.toString());
    }
  }

  /// Get weight records for a specific cattle
  Future<List<WeightRecordIsar>> getWeightRecordsForCattle(String cattleBusinessId) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle business ID is required');
      }

      return await _isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByMeasurementDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting weight records for cattle $cattleBusinessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve weight records for cattle', e.toString());
    }
  }

  /// Get weight record by business ID
  Future<WeightRecordIsar?> getWeightRecordByBusinessId(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Business ID is required');
      }

      return await _isar.weightRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting weight record by business ID $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve weight record', e.toString());
    }
  }

  /// Add a new weight record
  Future<WeightRecordIsar> addWeightRecord(WeightRecordIsar record) async {
    try {
      // Validate the record
      await _validateWeightRecord(record, isNew: true);

      // Generate business ID if needed
      if (record.businessId?.isEmpty ?? true) {
        record.businessId = _uuid.v4();
      }

      // Set audit fields
      final now = DateTime.now();
      record.createdAt = now;
      record.updatedAt = now;

      // Calculate growth data if previous weight exists
      await _calculateGrowthData(record);

      await _isar.writeTxn(() async {
        await _isar.weightRecordIsars.put(record);
      });

      // Notify about weight record addition
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyWeightChange({
        'action': 'add',
        'recordId': record.businessId,
        'cattleId': record.cattleBusinessId,
        'data': record.toMap(),
      });

      _logger.info('Successfully added weight record: ${record.businessId}');
      return record;
    } catch (e) {
      _logger.severe('Error adding weight record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add weight record', e.toString());
    }
  }

  /// Update an existing weight record
  Future<WeightRecordIsar> updateWeightRecord(WeightRecordIsar record) async {
    try {
      // Validate the record
      await _validateWeightRecord(record, isNew: false);

      // Set audit fields
      record.updatedAt = DateTime.now();

      // Recalculate growth data
      await _calculateGrowthData(record);

      await _isar.writeTxn(() async {
        await _isar.weightRecordIsars.put(record);
      });

      // Notify about weight record update
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyWeightChange({
        'action': 'update',
        'recordId': record.businessId,
        'cattleId': record.cattleBusinessId,
        'data': record.toMap(),
      });

      _logger.info('Successfully updated weight record: ${record.businessId}');
      return record;
    } catch (e) {
      _logger.severe('Error updating weight record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update weight record', e.toString());
    }
  }

  /// Delete a weight record
  Future<void> deleteWeightRecord(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Business ID is required');
      }

      final record = await getWeightRecordByBusinessId(businessId);
      if (record == null) {
        throw RecordNotFoundException('Weight record not found');
      }

      await _isar.writeTxn(() async {
        await _isar.weightRecordIsars.delete(record.id);
      });

      // Notify about weight record deletion
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyWeightChange({
        'action': 'delete',
        'recordId': businessId,
        'cattleId': record.cattleBusinessId,
      });

      _logger.info('Successfully deleted weight record: $businessId');
    } catch (e) {
      _logger.severe('Error deleting weight record: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete weight record', e.toString());
    }
  }

  /// Get weight records within a date range
  Future<List<WeightRecordIsar>> getWeightRecordsByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      return await _isar.weightRecordIsars
          .filter()
          .measurementDateBetween(startDate, endDate)
          .sortByMeasurementDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting weight records by date range: $e');
      throw DatabaseException('Failed to retrieve weight records by date range', e.toString());
    }
  }

  /// Get latest weight record for a cattle
  Future<WeightRecordIsar?> getLatestWeightRecord(String cattleBusinessId) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle business ID is required');
      }

      return await _isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByMeasurementDateDesc()
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting latest weight record for cattle $cattleBusinessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve latest weight record', e.toString());
    }
  }

  //=== WEIGHT GOALS ===//

  /// Get all weight goals
  Future<List<WeightGoalIsar>> getAllWeightGoals() async {
    try {
      return await _isar.weightGoalIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error getting all weight goals: $e');
      throw DatabaseException('Failed to retrieve weight goals', e.toString());
    }
  }

  /// Get weight goals for a specific cattle
  Future<List<WeightGoalIsar>> getWeightGoalsForCattle(String cattleBusinessId) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle business ID is required');
      }

      return await _isar.weightGoalIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByStartDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting weight goals for cattle $cattleBusinessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve weight goals for cattle', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate weight record data
  Future<void> _validateWeightRecord(WeightRecordIsar record, {required bool isNew}) async {
    if (record.cattleBusinessId?.isEmpty ?? true) {
      throw ValidationException('Cattle business ID is required');
    }

    if (record.weight <= 0) {
      throw ValidationException('Weight must be greater than 0');
    }

    if (record.measurementDate == null) {
      throw ValidationException('Measurement date is required');
    }

    // Check if cattle exists
    final cattle = await _isar.cattleIsars
        .filter()
        .businessIdEqualTo(record.cattleBusinessId!)
        .findFirst();
    
    if (cattle == null) {
      throw ValidationException('Cattle not found');
    }

    // Check for duplicate business ID if new record
    if (isNew && record.businessId?.isNotEmpty == true) {
      final existing = await getWeightRecordByBusinessId(record.businessId!);
      if (existing != null) {
        throw ValidationException('Weight record with this business ID already exists');
      }
    }
  }

  /// Calculate growth data for weight record
  Future<void> _calculateGrowthData(WeightRecordIsar record) async {
    try {
      if (record.cattleBusinessId?.isEmpty ?? true) return;

      // Get previous weight record for this cattle
      final previousRecords = await _isar.weightRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(record.cattleBusinessId!)
          .and()
          .measurementDateLessThan(record.measurementDate ?? DateTime.now())
          .sortByMeasurementDateDesc()
          .findAll();

      if (previousRecords.isNotEmpty) {
        final previousRecord = previousRecords.first;
        record.previousWeight = previousRecord.weight;
        record.weightGain = record.weight - previousRecord.weight;

        // Calculate days since last measurement
        if (previousRecord.measurementDate != null && record.measurementDate != null) {
          final daysDiff = record.measurementDate!.difference(previousRecord.measurementDate!).inDays;
          record.daysSinceLastMeasurement = daysDiff;

          // Calculate daily gain
          if (daysDiff > 0) {
            record.dailyGain = record.weightGain! / daysDiff;
          }
        }
      }
    } catch (e) {
      _logger.warning('Error calculating growth data: $e');
      // Don't throw error, just log warning as this is supplementary data
    }
  }
}
