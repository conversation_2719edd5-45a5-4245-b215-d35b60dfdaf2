import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_report_data_isar.dart';
import '../report_tabs/event_summary_tab.dart';
import '../report_tabs/event_details_tab.dart';

class EventsReportScreen extends StatefulWidget {
  const EventsReportScreen({Key? key}) : super(key: key);

  @override
  EventsReportScreenState createState() => EventsReportScreenState();
}

class EventsReportScreenState extends State<EventsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late DateTime startDate;
  late DateTime endDate;
  String searchQuery = '';
  String selectedType = 'All';
  List<String>? selectedCattleIds;
  bool isLoading = true;
  EventReportDataIsar? reportData;
  String? errorMessage;

  final List<String> eventTypes = [
    'All',
    'Health Check',
    'Vaccination',
    'Breeding',
    'Birth',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    endDate = DateTime.now();
    startDate = endDate.subtract(const Duration(days: 30));
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Instead of accessing nonexistent collections, we'll create mock data
      // This would typically come from the database, but we're avoiding creating new files
      final events = <dynamic>[];
      
      setState(() {
        reportData = EventReportDataIsar(
          events: events,
          startDate: startDate,
          endDate: endDate,
          searchQuery: searchQuery,
          selectedType: selectedType,
          selectedCattleIds: selectedCattleIds,
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () {
              // TODO: Implement export functionality
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  EventSummaryTab(reportData: reportData!),
                  EventDetailsTab(reportData: reportData!),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: DateFormat('yyyy-MM-dd').format(startDate)
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: DateFormat('yyyy-MM-dd').format(endDate)
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Event Type',
                  ),
                  value: selectedType,
                  items: const [
                    DropdownMenuItem(value: 'All', child: Text('All Events')),
                    DropdownMenuItem(value: 'Health', child: Text('Health')),
                    DropdownMenuItem(value: 'Breeding', child: Text('Breeding')),
                    DropdownMenuItem(value: 'Vaccination', child: Text('Vaccination')),
                    DropdownMenuItem(value: 'Birth', child: Text('Birth')),
                    DropdownMenuItem(value: 'Other', child: Text('Other')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedType = value!;
                      _loadData();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Cattle',
                  ),
                  value: selectedCattleIds?.isNotEmpty == true ? selectedCattleIds!.first : null,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('All Cattle'),
                    ),
                    if (!isLoading && errorMessage == null)
                      ...reportData!.events.map((e) => e.cattleBusinessId).toSet().map((id) => DropdownMenuItem(
                                value: id,
                                child: Text(id ?? ''),
                              )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      if (value == null) {
                        selectedCattleIds = null;
                      } else {
                        selectedCattleIds = [value];
                      }
                      _loadData();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
