import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'date_range_themes.dart';
import 'date_range_constants.dart';
import 'date_filter_dialog.dart';

/// Universal date range filter widget with consistent layout across all modules
///
/// This widget follows modern Flutter patterns:
/// - Clean enum-based theming system
/// - Unified callback API
/// - Proper state encapsulation
/// - Type-safe configuration
/// - Responsive design
///
/// Usage Example:
/// ```dart
/// DateRangeFilterWidget(
///   theme: DateRangeTheme.weight,
///   startDate: startDate,
///   endDate: endDate,
///   onRangeChanged: (start, end) => setState(() {
///     startDate = start;
///     endDate = end;
///   }),
///   onFiltersChanged: _refreshData,
/// )
/// ```
class DateRangeFilterWidget extends StatefulWidget {

  final DateTime? startDate;
  final DateTime? endDate;
  final DateRangeTheme theme;
  final Function(DateTime?, DateTime?) onRangeChanged;
  final VoidCallback? onFiltersChanged;

  // UI customization for flexible layouts
  final bool compact;
  final bool iconOnly;
  final EdgeInsets? padding;
  final double? buttonHeight;
  final double? activeBackgroundOpacity;

  const DateRangeFilterWidget({
    Key? key,
    this.startDate,
    this.endDate,
    required this.theme,
    required this.onRangeChanged,
    this.onFiltersChanged,
    this.compact = false,
    this.iconOnly = false,
    this.padding,
    this.buttonHeight,
    this.activeBackgroundOpacity,
  }) : super(key: key);

  @override
  State<DateRangeFilterWidget> createState() => _DateRangeFilterWidgetState();

  /// Helper method to get the theme color
  Color get themeColor => theme.color;
}

class _DateRangeFilterWidgetState extends State<DateRangeFilterWidget> {
  @override
  Widget build(BuildContext context) {
    final hasActiveFilter = _hasActiveFilter();

    if (widget.iconOnly) {
      return _buildIconOnlyButton(context, hasActiveFilter);
    }

    return Container(
      color: Colors.white,
      padding: widget.padding ?? EdgeInsets.all(widget.compact ? 4.0 : 8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: widget.buttonHeight ?? (widget.compact ? 44 : 48),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: hasActiveFilter ? widget.themeColor : Colors.white,
                    border: Border.all(
                      color: hasActiveFilter
                          ? widget.themeColor
                          : widget.themeColor.withValues(alpha: 0.3),
                      width: hasActiveFilter ? 2 : 1.5,
                    ),
                    boxShadow: hasActiveFilter
                        ? [
                            BoxShadow(
                              color: widget.themeColor.withValues(alpha: 0.25),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.15),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _showDateFilterDialog(context),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: widget.compact ? 12 : 16,
                          vertical: widget.compact ? 8 : 12,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: widget.compact ? 16 : 18,
                              color: hasActiveFilter ? Colors.white : widget.themeColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Date', // Full descriptive name
                                style: TextStyle(
                                  color: hasActiveFilter ? Colors.white : widget.themeColor,
                                  fontSize: widget.compact ? 12 : 14,
                                  fontWeight: hasActiveFilter ? FontWeight.w600 : FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                                
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIconOnlyButton(BuildContext context, bool hasActiveFilter) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(4.0),
      child: SizedBox(
        width: widget.buttonHeight ?? (widget.compact ? 40 : 48),
        height: widget.buttonHeight ?? (widget.compact ? 40 : 48),
        child: IconButton(
          onPressed: () => _showDateFilterDialog(context),
          icon: Stack(
            children: [
              Icon(
                Icons.date_range,
                size: widget.compact ? 18 : 24,
                color: hasActiveFilter ? widget.themeColor : Colors.grey[600],
              ),
              if (hasActiveFilter)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: widget.compact ? 8 : 10,
                    height: widget.compact ? 8 : 10,
                    decoration: BoxDecoration(
                      color: widget.themeColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          style: IconButton.styleFrom(
            backgroundColor: hasActiveFilter 
                ? widget.themeColor.withValues(alpha: widget.activeBackgroundOpacity ?? 0.1)
                : Colors.transparent,
            side: hasActiveFilter 
                ? BorderSide(color: widget.themeColor.withValues(alpha: 0.3))
                : null,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          tooltip: hasActiveFilter 
              ? _getDateRangeDisplayText()
              : 'Filter by date range',
        ),
      ),
    );
  }

  bool _hasActiveFilter() {
    return widget.startDate != null && widget.endDate != null;
  }

  void _showDateFilterDialog(BuildContext context) async {
    final result = await showDialog<Map<String, DateTime?>>(
      context: context,
      builder: (BuildContext context) {
        return DateFilterDialog(
          currentStartDate: widget.startDate,
          currentEndDate: widget.endDate,
          themeColor: widget.themeColor,
        );
      },
    );

    if (result != null) {
      widget.onRangeChanged(result['startDate'], result['endDate']);
      widget.onFiltersChanged?.call();
    }
  }

  /// Get the appropriate display text for the current date range selection
  String _getDateRangeDisplayText() {
    if (widget.startDate == null || widget.endDate == null) {
      return 'Date Filter';  // Default text when no filter is active
    }

    // Check for specific date range patterns using type-safe enum
    final activePreset = _getActivePreset();
    if (activePreset != null) {
      return activePreset.label;
    } else {
      // Custom date range - show the actual dates
      return _formatCustomDateRange();
    }
  }

  /// Get the active preset if the current date range matches any preset
  DateRangePreset? _getActivePreset() {
    if (widget.startDate == null || widget.endDate == null) return null;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (final preset in DateRangePreset.values) {
      if (preset.days == null) continue; // Skip custom preset

      final days = preset.days!;
      bool matches = false;

      if (days == 0) {
        // Today: both start and end should be today
        matches = _isSameDay(widget.startDate!, today) && _isSameDay(widget.endDate!, today);
      } else if (days == 1) {
        // Yesterday: both start and end should be yesterday
        final yesterday = today.subtract(const Duration(days: 1));
        matches = _isSameDay(widget.startDate!, yesterday) && _isSameDay(widget.endDate!, yesterday);
      } else {
        // For "Last X Days": check if start date is exactly X days ago and end is today/now
        final expectedStartDate = today.subtract(Duration(days: days));
        final startMatches = _isSameDay(widget.startDate!, expectedStartDate);
        final endIsToday = _isSameDay(widget.endDate!, today);
        matches = startMatches && endIsToday;
      }

      if (matches) return preset;
    }

    return null;
  }

  /// Format custom date range for display
  String _formatCustomDateRange() {
    if (widget.startDate == null || widget.endDate == null) return '';
    
    final formatter = DateFormat('MMM dd');
    final startFormatted = formatter.format(widget.startDate!);
    final endFormatted = formatter.format(widget.endDate!);
    
    // If same year, don't repeat it
    if (widget.startDate!.year == widget.endDate!.year) {
      return '$startFormatted - $endFormatted';
    } else {
      final formatterWithYear = DateFormat('MMM dd, yyyy');
      return '${formatterWithYear.format(widget.startDate!)} - ${formatterWithYear.format(widget.endDate!)}';
    }
  }

  /// Helper method to check if two dates are on the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
