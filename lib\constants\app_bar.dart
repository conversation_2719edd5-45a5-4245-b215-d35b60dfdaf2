import 'package:flutter/material.dart';
import 'app_colors.dart';

/// A utility class that provides standardized AppBar configurations for the app.
class AppBarConfig {
  /// Creates a standard AppBar with the app's theme
  ///
  /// [title] - The title to display
  /// [actions] - Optional list of action widgets to display on the right
  /// [leading] - Optional widget to display on the left
  /// [elevation] - Optional elevation value (defaults to 0)
  /// [bottom] - Optional widget to display at the bottom of the AppBar (e.g., TabBar)
  /// [centerTitle] - Whether to center the title (defaults to false)
  static AppBar standard({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    double elevation = 0,
    PreferredSizeWidget? bottom,
    bool centerTitle = false,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: elevation,
      actions: actions,
      leading: leading,
      bottom: bottom,
      centerTitle: centerTitle,
    );
  }

  /// Creates an AppBar with a drawer toggle button
  ///
  /// [title] - The title to display
  /// [context] - The build context
  /// [actions] - Optional list of action widgets to display on the right
  static AppBar withDrawer({
    required String title,
    required BuildContext context,
    List<Widget>? actions,
    double elevation = 0,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: elevation,
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu_outlined),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
        ),
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  /// Creates an AppBar with a back button
  ///
  /// [title] - The title to display
  /// [context] - The build context
  /// [actions] - Optional list of action widgets to display on the right
  /// [onBack] - Optional callback when back button is pressed (defaults to Navigator.pop)
  static AppBar withBack({
    required String title,
    required BuildContext context,
    List<Widget>? actions,
    VoidCallback? onBack,
    double elevation = 0,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: elevation,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: onBack ?? () => Navigator.pop(context),
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  /// Creates a common notifications button for the AppBar
  static IconButton notificationsButton({
    required VoidCallback onPressed,
    String tooltip = 'Notifications',
  }) {
    return IconButton(
      icon: const Icon(Icons.notifications_outlined),
      onPressed: onPressed,
      tooltip: tooltip,
    );
  }

  /// Creates a save button for the AppBar
  static IconButton saveButton({
    required VoidCallback onPressed,
    String tooltip = 'Save',
  }) {
    return IconButton(
      icon: const Icon(Icons.save),
      onPressed: onPressed,
      tooltip: tooltip,
    );
  }

  /// Creates a loading indicator in place of an action button
  static Widget loadingIndicator({
    double size = 24.0,
    double padding = 12.0,
  }) {
    return Container(
      width: 48,
      height: 48,
      padding: EdgeInsets.all(padding),
      child: const CircularProgressIndicator(
        strokeWidth: 2,
        color: Colors.white,
      ),
    );
  }
}
