import 'package:flutter/material.dart';
import '../controllers/filter_controller.dart';
import '../config/filter_config.dart';

/// Completely self-sufficient dialog widget for filter selection
///
/// This dialog follows the DateRangeFilterWidget pattern and only accepts
/// data (controller, options, theme) and handles all logic internally.
/// It updates the controller directly and returns via Navigator.pop().
class FilterDialog extends StatefulWidget {
  final FilterController controller;
  final List<FilterField> options;
  final Color themeColor;

  const FilterDialog({
    Key? key,
    required this.controller,
    required this.options,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  // Local state for dialog - we'll sync with controller on apply
  late Map<String, dynamic> _localFilterValues;

  @override
  void initState() {
    super.initState();
    // Initialize with current controller values
    _localFilterValues = Map.from(widget.controller.activeFilterValues);
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isCompact = screenSize.width < 600;
    const animationDuration = Duration(milliseconds: 200);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: AnimatedContainer(
        duration: animationDuration,
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.9,
          maxHeight: screenSize.height * 0.8,
          minWidth: 300,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCompactHeader(isCompact),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isCompact ? 12 : 16),
                child: _buildCompactContent(isCompact),
              ),
            ),
            _buildCompactActions(isCompact),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactHeader(bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.themeColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.tune,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Filter Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  // Keep old method for backward compatibility
  Widget _buildHeader() {
    return _buildCompactHeader(false);
  }

  Widget _buildCompactContent(bool isCompact) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ...widget.options.map((field) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildCompactFilterField(field, isCompact),
        )),
      ],
    );
  }

  Widget _buildSectionHeader(String title, bool isCompact) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 8 : 12,
        vertical: isCompact ? 6 : 8,
      ),
      decoration: BoxDecoration(
        color: widget.themeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: isCompact ? 13 : 14,
          fontWeight: FontWeight.w600,
          color: widget.themeColor,
        ),
      ),
    );
  }

  // Keep old method for backward compatibility
  Widget _buildContent() {
    return _buildCompactContent(false);
  }

  Widget _buildCompactFilterField(FilterField field, bool isCompact) {
    // Check if field has valid options
    if (!field.hasValidOptions) {
      return _buildErrorField(field, 'No options configured', isCompact);
    }

    // Check if field has dependencies and if they are satisfied
    if (field.hasDependencies && !widget.controller.areDependenciesSatisfied(field.dependsOn)) {
      return _buildDisabledField(field, isCompact);
    }

    // Use dynamic loading if available, otherwise use static options
    if (field.isDynamic) {
      return _buildDynamicFilterField(field, isCompact);
    } else {
      return _buildStaticFilterField(field, isCompact);
    }
  }

  Widget _buildDynamicFilterField(FilterField field, bool isCompact) {
    // Get dependency values for the options loader
    Map<String, dynamic>? dependencies = widget.controller.getDependencyValues(field.dependsOn);

    // Special handling for breed field - always pass animalType even if not formally dependent
    if (field.key == 'breed') {
      dependencies ??= {};
      final animalType = _localFilterValues['animalType'] as String?;
      if (animalType != null) {
        dependencies['animalType'] = animalType;
      }
    }

    return FutureBuilder<List<String>>(
      key: ValueKey('${field.key}_${dependencies.toString()}'), // Rebuild when dependencies change
      future: field.optionsLoader!(dependencies),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingField(field, isCompact);
        } else if (snapshot.hasError) {
          return _buildErrorField(field, snapshot.error.toString(), isCompact);
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _buildEmptyField(field, isCompact);
        } else {
          return _buildStaticFilterFieldWithOptions(field, snapshot.data!, isCompact);
        }
      },
    );
  }

  Widget _buildStaticFilterField(FilterField field, bool isCompact) {
    return _buildStaticFilterFieldWithOptions(field, field.options!, isCompact);
  }

  Widget _buildStaticFilterFieldWithOptions(FilterField field, List<String> options, bool isCompact) {
    switch (field.type) {
      case FilterInputType.dropdown:
        return _buildCompactDropdownField(field, options, isCompact);
      case FilterInputType.multiSelectChip:
        return _buildCompactMultiSelectChipField(field, options, isCompact);
    }
  }

  // Keep old method for backward compatibility
  Widget _buildFilterField(FilterField field) {
    return _buildCompactFilterField(field, false);
  }

  Widget _buildDisabledField(FilterField field, bool isCompact) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[400],
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            'Select ${field.dependsOn?.join(', ')} first',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: isCompact ? 13 : 14,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingField(FilterField field, bool isCompact) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: widget.themeColor,
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          decoration: BoxDecoration(
            color: widget.themeColor.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: widget.themeColor.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              SizedBox(
                width: isCompact ? 16 : 20,
                height: isCompact ? 16 : 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
                ),
              ),
              SizedBox(width: isCompact ? 8 : 12),
              Text(
                'Loading options...',
                style: TextStyle(
                  color: widget.themeColor,
                  fontSize: isCompact ? 13 : 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorField(FilterField field, String error, bool isCompact) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: Colors.red[700],
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red[300]!),
          ),
          child: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red[700], size: isCompact ? 16 : 18),
              SizedBox(width: isCompact ? 8 : 12),
              Expanded(
                child: Text(
                  'Error loading options',
                  style: TextStyle(
                    color: Colors.red[700],
                    fontSize: isCompact ? 13 : 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyField(FilterField field, bool isCompact) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isCompact ? 12 : 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            'No options available',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: isCompact ? 13 : 14,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(FilterField field) {
    final currentValue = _localFilterValues[field.key] as String?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: widget.themeColor,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: currentValue,
          decoration: InputDecoration(
            hintText: field.placeholder ?? 'Select ${field.label.toLowerCase()}',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: widget.themeColor,
                width: 2,
              ),
            ),
          ),
          items: [
            // Add a "None" option to allow clearing the selection
            DropdownMenuItem<String>(
              value: null,
              child: Text(
                'None',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ...field.options!.map((option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(option),
              );
            }),
          ],
          onChanged: (String? newValue) {
            setState(() {
              if (newValue == null) {
                _localFilterValues.remove(field.key);
              } else {
                _localFilterValues[field.key] = newValue;
              }
            });
          },
        ),
      ],
    );
  }

  Widget _buildMultiSelectChipField(FilterField field) {
    final currentValues = _localFilterValues[field.key] as List<String>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: widget.themeColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: field.options!.map((option) {
            final isSelected = currentValues.contains(option);
            return FilterChip(
              label: Text(option),
              selected: isSelected,
              onSelected: (bool selected) {
                setState(() {
                  final newValues = List<String>.from(currentValues);
                  if (selected) {
                    newValues.add(option);
                  } else {
                    newValues.remove(option);
                  }
                  
                  if (newValues.isEmpty) {
                    _localFilterValues.remove(field.key);
                  } else {
                    _localFilterValues[field.key] = newValues;
                  }
                });
              },
              selectedColor: widget.themeColor.withValues(alpha: 0.2),
              checkmarkColor: widget.themeColor,
              side: BorderSide(
                color: isSelected ? widget.themeColor : Colors.grey[300]!,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCompactDropdownField(FilterField field, List<String> options, bool isCompact) {
    final currentValue = _localFilterValues[field.key] as String?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: widget.themeColor,
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        DropdownButtonFormField<String>(
          value: currentValue,
          decoration: InputDecoration(
            labelText: field.label,
            hintText: field.placeholder ?? 'Select ${field.label.toLowerCase()}',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 16,
            ),
            prefixIcon: Icon(
              _getFieldIcon(field.key),
              color: widget.themeColor,
            ),
          ),
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: isCompact ? 13 : 14,
                  ),
                ),
              );
            }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              if (newValue == null || newValue.isEmpty) {
                _localFilterValues.remove(field.key);
              } else {
                _localFilterValues[field.key] = newValue;
              }

              // Handle dependencies - clear dependent filters when this changes
              final dependentKeys = widget.options
                  .where((f) => f.dependsOn?.contains(field.key) == true)
                  .map((f) => f.key)
                  .toList();

              for (final dependentKey in dependentKeys) {
                _localFilterValues.remove(dependentKey);
              }

              // Special handling: clear breed when animal type changes
              if (field.key == 'animalType') {
                _localFilterValues.remove('breed');
              }
            });
          },
        ),
      ],
    );
  }

  Widget _buildCompactMultiSelectChipField(FilterField field, List<String> options, bool isCompact) {
    final currentValues = _localFilterValues[field.key] as List<String>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: widget.themeColor,
          ),
        ),
        SizedBox(height: isCompact ? 6 : 8),
        Wrap(
          spacing: isCompact ? 6 : 8,
          runSpacing: isCompact ? 6 : 8,
          children: options.map((option) {
            final isSelected = currentValues.contains(option);
            return FilterChip(
              label: Text(
                option,
                style: TextStyle(
                  fontSize: isCompact ? 12 : 13,
                ),
              ),
              selected: isSelected,
              onSelected: (bool selected) {
                setState(() {
                  final newValues = List<String>.from(currentValues);
                  if (selected) {
                    newValues.add(option);
                  } else {
                    newValues.remove(option);
                  }

                  if (newValues.isEmpty) {
                    _localFilterValues.remove(field.key);
                  } else {
                    _localFilterValues[field.key] = newValues;
                  }
                });
              },
              selectedColor: widget.themeColor.withValues(alpha: 0.2),
              checkmarkColor: widget.themeColor,
              side: BorderSide(
                color: isSelected ? widget.themeColor : Colors.grey[300]!,
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: isCompact ? VisualDensity.compact : VisualDensity.standard,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCompactActions(bool isCompact) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                setState(() {
                  _localFilterValues.clear();
                });
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Clear All'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Apply Filters',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Keep old method for backward compatibility
  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _localFilterValues.clear();
                });
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: widget.themeColor),
              ),
              child: Text(
                'Clear All',
                style: TextStyle(color: widget.themeColor),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Apply'),
            ),
          ),
        ],
      ),
    );
  }

  void _applyFilters() {
    // Update the controller with local values
    // First, clear existing filters that are not in local values
    final keysToRemove = widget.controller.activeFilterValues.keys
        .where((key) => !_localFilterValues.containsKey(key))
        .toList();

    for (final key in keysToRemove) {
      widget.controller.updateFilter(key, null);
    }

    // Then update/add new values with dependency handling
    _localFilterValues.forEach((key, value) {
      // Find dependent fields for this filter
      final dependentKeys = widget.options
          .where((f) => f.dependsOn?.contains(key) == true)
          .map((f) => f.key)
          .toList();

      widget.controller.updateFilter(key, value, clearDependents: dependentKeys);
    });

    Navigator.of(context).pop(true);
  }

  /// Get appropriate icon for each filter field
  IconData _getFieldIcon(String fieldKey) {
    switch (fieldKey.toLowerCase()) {
      case 'cattle':
        return Icons.pets;
      case 'animaltype':
      case 'animal_type':
        return Icons.category;
      case 'breed':
        return Icons.pets_outlined;
      case 'gender':
        return Icons.wc;
      case 'weightrange':
      case 'weight_range':
        return Icons.scale;
      case 'measurementmethod':
      case 'measurement_method':
        return Icons.straighten;
      case 'status':
        return Icons.info_outline;
      case 'method':
        return Icons.build;
      default:
        return Icons.filter_alt;
    }
  }
}
