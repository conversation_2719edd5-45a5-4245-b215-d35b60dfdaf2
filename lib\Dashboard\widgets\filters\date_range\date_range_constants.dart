/// Type-safe enum for date range presets
enum DateRangePreset {
  today,
  yesterday,
  last7Days,
  last30Days,
  last90Days,
  last6Months,
  custom,
}

/// Extension providing configuration properties for date range presets
extension DateRangePresetExtension on DateRangePreset {
  /// Display label for the preset
  String get label {
    switch (this) {
      case DateRangePreset.today:
        return 'Today';
      case DateRangePreset.yesterday:
        return 'Yesterday';
      case DateRangePreset.last7Days:
        return 'Last 7 Days';
      case DateRangePreset.last30Days:
        return 'Last 30 Days';
      case DateRangePreset.last90Days:
        return 'Last 90 Days';
      case DateRangePreset.last6Months:
        return 'Last 6 Months';
      case DateRangePreset.custom:
        return 'Custom Range';
    }
  }

  /// Whether to show the value in the display
  bool get showValue {
    switch (this) {
      case DateRangePreset.today:
      case DateRangePreset.yesterday:
      case DateRangePreset.last7Days:
      case DateRangePreset.last30Days:
      case DateRangePreset.last90Days:
      case DateRangePreset.last6Months:
      case DateRangePreset.custom:
        return false; // All presets currently don't show values
    }
  }

  /// Whether to show the label in the display
  bool get showLabel {
    switch (this) {
      case DateRangePreset.today:
      case DateRangePreset.yesterday:
      case DateRangePreset.last7Days:
      case DateRangePreset.last30Days:
      case DateRangePreset.last90Days:
      case DateRangePreset.last6Months:
      case DateRangePreset.custom:
        return true; // All presets show labels
    }
  }

  /// Number of days for the preset (null for custom)
  int? get days {
    switch (this) {
      case DateRangePreset.today:
        return 0;
      case DateRangePreset.yesterday:
        return 1;
      case DateRangePreset.last7Days:
        return 7;
      case DateRangePreset.last30Days:
        return 30;
      case DateRangePreset.last90Days:
        return 90;
      case DateRangePreset.last6Months:
        return 180;
      case DateRangePreset.custom:
        return null; // Custom ranges don't have a fixed day count
    }
  }

  /// Whether this preset represents a fixed number of days
  bool get isFixedDays => days != null;

  /// Whether this preset is for custom date ranges
  bool get isCustom => this == DateRangePreset.custom;
}
