import 'package:flutter/foundation.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';

/// Centralized controller for individual cattle weight detail screens.
/// Manages all state, filtering, sorting, and business logic for both
/// Records and Analytics tabs, ensuring perfect synchronization.
class CattleWeightDetailController extends ChangeNotifier {
  final CattleIsar cattle;
  final WeightService _weightService = WeightService();

  // Core data
  List<WeightRecordIsar> _allRecords = [];
  List<WeightRecordIsar> _filteredRecords = [];
  bool _isLoading = false;
  String? _error;

  // Filter and sort state
  DateTime? _startDate;
  DateTime? _endDate;
  String? _sortBy;
  bool _sortAscending = true;

  // Analytics cache
  AnalyticsSummary? _analyticsSummary;

  CattleWeightDetailController({required this.cattle});

  // Getters
  List<WeightRecordIsar> get allRecords => List.unmodifiable(_allRecords);
  List<WeightRecordIsar> get filteredRecords => List.unmodifiable(_filteredRecords);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _allRecords.isNotEmpty;
  bool get hasFilteredData => _filteredRecords.isNotEmpty;

  // Filter getters
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  // Analytics getter
  AnalyticsSummary get analyticsSummary {
    if (_analyticsSummary == null) {
      _calculateAnalytics();
    }
    return _analyticsSummary!;
  }

  /// Initialize the controller by loading data
  Future<void> initialize() async {
    await loadData();
  }

  /// Load weight records for this cattle
  Future<void> loadData() async {
    try {
      _setLoading(true);
      _error = null;

      final records = await _weightService.getWeightRecordsByCattle(
        cattle.businessId!,
      );

      _allRecords = records;
      _applyFiltersAndSort();
      _calculateAnalytics();
      
      _setLoading(false);
    } catch (e) {
      _error = 'Error loading weight records: $e';
      _setLoading(false);
      rethrow;
    }
  }

  /// Refresh data from the database
  Future<void> refresh() async {
    await loadData();
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    if (_startDate == startDate && _endDate == endDate) return;
    
    _startDate = startDate;
    _endDate = endDate;
    _applyFiltersAndSort();
    _calculateAnalytics();
    notifyListeners();
  }

  /// Set sort criteria
  void setSortBy(String? sortBy, {bool? ascending}) {
    bool changed = false;
    
    if (_sortBy != sortBy) {
      _sortBy = sortBy;
      changed = true;
    }
    
    if (ascending != null && _sortAscending != ascending) {
      _sortAscending = ascending;
      changed = true;
    }
    
    if (changed) {
      _applyFiltersAndSort();
      notifyListeners();
    }
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _sortBy = null;
    _sortAscending = true;
    _applyFiltersAndSort();
    _calculateAnalytics();
    notifyListeners();
  }

  /// Add a new weight record
  Future<void> addRecord(WeightRecordIsar record) async {
    try {
      await _weightService.addWeightRecord(record);
      await refresh(); // Reload all data to ensure consistency
    } catch (e) {
      _error = 'Error adding weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Update an existing weight record
  Future<void> updateRecord(WeightRecordIsar record) async {
    try {
      await _weightService.updateWeightRecord(record);
      await refresh(); // Reload all data to ensure consistency
    } catch (e) {
      _error = 'Error updating weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Delete a weight record
  Future<void> deleteRecord(String recordId) async {
    try {
      await _weightService.deleteWeightRecord(recordId);
      await refresh(); // Reload all data to ensure consistency
    } catch (e) {
      _error = 'Error deleting weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Apply filters and sorting to the data
  void _applyFiltersAndSort() {
    List<WeightRecordIsar> tempRecords = List.from(_allRecords);

    // Apply date filter
    if (_startDate != null && _endDate != null) {
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      tempRecords = tempRecords.where((record) {
        final date = record.measurementDate;
        if (date == null) return false;
        return !date.isBefore(_startDate!) && date.isBefore(inclusiveEndDate);
      }).toList();
    }

    // Apply sorting
    if (_sortBy != null) {
      tempRecords.sort((a, b) {
        int comparison;
        switch (_sortBy) {
          case 'Date':
            comparison = (a.measurementDate ?? DateTime(0))
                .compareTo(b.measurementDate ?? DateTime(0));
            if (comparison == 0) {
              comparison = (a.createdAt ?? DateTime(0))
                  .compareTo(b.createdAt ?? DateTime(0));
            }
            break;
          case 'Weight':
            comparison = a.weight.compareTo(b.weight);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    } else {
      // Default sorting: Newest first
      tempRecords.sort((a, b) {
        final dateComparison = (b.measurementDate ?? DateTime(0))
            .compareTo(a.measurementDate ?? DateTime(0));
        if (dateComparison == 0) {
          return (b.createdAt ?? DateTime(0))
              .compareTo(a.createdAt ?? DateTime(0));
        }
        return dateComparison;
      });
    }

    _filteredRecords = tempRecords;
  }

  /// Calculate analytics based on filtered data
  void _calculateAnalytics() {
    if (_filteredRecords.isEmpty) {
      _analyticsSummary = AnalyticsSummary.empty();
      return;
    }

    final sortedRecords = List<WeightRecordIsar>.from(_filteredRecords);
    sortedRecords.sort((a, b) =>
        (a.measurementDate ?? DateTime(0)).compareTo(b.measurementDate ?? DateTime(0)));

    final currentWeight = sortedRecords.last.weight;
    final totalWeight = _filteredRecords.map((r) => r.weight).reduce((a, b) => a + b);
    final averageWeight = totalWeight / _filteredRecords.length;
    
    double weightGain = 0.0;
    double averageDailyGain = 0.0;
    
    if (sortedRecords.length >= 2) {
      final firstWeight = sortedRecords.first.weight;
      final lastWeight = sortedRecords.last.weight;
      weightGain = lastWeight - firstWeight;
      
      final firstDate = sortedRecords.first.measurementDate;
      final lastDate = sortedRecords.last.measurementDate;
      
      if (firstDate != null && lastDate != null) {
        final daysDifference = lastDate.difference(firstDate).inDays;
        if (daysDifference > 0) {
          averageDailyGain = weightGain / daysDifference;
        }
      }
    }

    _analyticsSummary = AnalyticsSummary(
      currentWeight: currentWeight,
      averageWeight: averageWeight,
      weightGain: weightGain,
      averageDailyGain: averageDailyGain,
      totalRecords: _filteredRecords.length,
      dateRange: _startDate != null && _endDate != null 
          ? '${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}'
          : 'All time',
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }


}

/// Analytics summary data class
class AnalyticsSummary {
  final double currentWeight;
  final double averageWeight;
  final double weightGain;
  final double averageDailyGain;
  final int totalRecords;
  final String dateRange;

  const AnalyticsSummary({
    required this.currentWeight,
    required this.averageWeight,
    required this.weightGain,
    required this.averageDailyGain,
    required this.totalRecords,
    required this.dateRange,
  });

  factory AnalyticsSummary.empty() {
    return const AnalyticsSummary(
      currentWeight: 0.0,
      averageWeight: 0.0,
      weightGain: 0.0,
      averageDailyGain: 0.0,
      totalRecords: 0,
      dateRange: 'No data',
    );
  }
}
