/// Universal Filter System Components
///
/// This package provides a complete, controller-driven filter system
/// following the established DateRangeFilterWidget pattern.

// Core controller
export 'controllers/filter_controller.dart';

// Configuration models
export 'config/filter_config.dart';
export 'config/module_configs.dart';

// Individual filter components
export 'search_widget.dart';
export 'sort_widget.dart';
export 'filter_status_bar.dart';

// Custom filter components
export 'custom_filters/index.dart';

// Date range components
export 'date_range/index.dart';

// Layout components
export 'layouts/index.dart';
