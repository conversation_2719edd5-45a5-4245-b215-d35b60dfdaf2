import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';

class TransactionFormDialog extends StatefulWidget {
  final Function(TransactionIsar) onSave;
  final List<CategoryIsar> categories;
  final TransactionIsar? transaction;

  const TransactionFormDialog({
    Key? key,
    required this.onSave,
    required this.categories,
    this.transaction,
  }) : super(key: key);

  @override
  State<TransactionFormDialog> createState() => _TransactionFormDialogState();
}

class _TransactionFormDialogState extends State<TransactionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _selectedDate;
  late String _selectedType;
  String? _selectedCategory;
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  String? _selectedPaymentMethod;

  final List<String> _paymentMethods = [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'Mobile Payment',
    'Check',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.transaction?.date ?? DateTime.now();
    _selectedType = widget.transaction?.categoryType ?? 'Income';
    _selectedCategory = widget.transaction?.category;
    _amountController = TextEditingController(
      text: widget.transaction?.amount.toString() ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.transaction?.description ?? '',
    );
    _selectedPaymentMethod =
        widget.transaction?.paymentMethod ?? _paymentMethods[0];
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  List<String> _getCategoriesForType(String type) {
    return widget.categories
        .where((category) => category.type == type)
        .map((category) => category.name)
        .toList();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _handleTypeChange(String? newValue) {
    if (newValue != null) {
      setState(() {
        _selectedType = newValue;
        _selectedCategory = null;
      });
    }
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final transaction = widget.transaction ?? TransactionIsar();

      // Update transaction fields
      transaction
        ..date = _selectedDate
        ..categoryType = _selectedType
        ..category = _selectedCategory ?? ''
        ..title = _selectedCategory ?? 'Transaction' // Set title to category or default
        ..amount = double.parse(_amountController.text)
        ..description = _descriptionController.text
        ..paymentMethod = _selectedPaymentMethod ?? ''
        ..updatedAt = DateTime.now();

      // Set created date for new transactions
      if (widget.transaction == null) {
        transaction.createdAt = DateTime.now();
      }

      widget.onSave(transaction);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesForType = _getCategoriesForType(_selectedType);

    return AlertDialog(
      title: Text(
          widget.transaction == null ? 'Add Transaction' : 'Edit Transaction'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text(
                  'Date: ${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                ),
                trailing: const Icon(Icons.calendar_today),
                onTap: () => _selectDate(context),
              ),
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(labelText: 'Type'),
                items: ['Income', 'Expense'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: _handleTypeChange,
                validator: (value) =>
                    value == null ? 'Please select a type' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Category'),
                items: categoriesForType.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
                validator: (value) =>
                    value == null ? 'Please select a category' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount',
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPaymentMethod,
                decoration: const InputDecoration(labelText: 'Payment Method'),
                items: _paymentMethods.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedPaymentMethod = newValue;
                  });
                },
                validator: (value) =>
                    value == null ? 'Please select a payment method' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _handleSubmit,
          child: const Text('Save'),
        ),
      ],
    );
  }
}
