import 'package:flutter/material.dart';
import '../controllers/filter_controller.dart';
import '../config/filter_config.dart';
import '../date_range/date_range_filter_widget.dart';
import '../search_widget.dart';
import '../sort_widget.dart';
import '../custom_filters/filter_widget.dart';
import '../filter_status_bar.dart';

/// Complete filter layout for comprehensive filtering, sorting, and searching
///
/// This layout provides the optimal filtering interface with:
/// **Row 1**: Filter + Date + Sort (main filtering controls)
/// **FilterStatusBar**: Clear filters + record count
/// **Row 2**: Search (full-width search input)
///
/// Features:
/// - Custom filter options (dropdown, multi-select, etc.)
/// - Date range filtering
/// - Sort controls with direction toggle
/// - Debounced search functionality
/// - Filter status bar with clear all functionality
/// - Responsive design that adapts to screen size
///
/// Usage Example:
/// ```dart
/// FullFilterLayout(
///   controller: filterController,
///   config: ModuleConfigs.weight,
///   totalCount: totalRecords,
///   filteredCount: filteredRecords,
/// )
/// ```
class FullFilterLayout extends StatelessWidget {
  final FilterController controller;
  final ModuleFilterConfig config;
  final int? totalCount;
  final int? filteredCount;
  final bool compact;
  final EdgeInsets? padding;

  const FullFilterLayout({
    Key? key,
    required this.controller,
    required this.config,
    this.totalCount,
    this.filteredCount,
    this.compact = false,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: padding ?? EdgeInsets.all(compact ? 8.0 : 16.0),
      child: Column(
        children: [
          // Row 1: Filter + Date + Sort (all compact with consistent height)
          Row(
            children: [
              if (config.hasFilters)
                Expanded(
                  child: FilterWidget(
                    controller: controller,
                    options: config.filters,
                    theme: controller.theme,
                    compact: true,
                    buttonHeight: 44,
                  ),
                ),
              if (config.hasFilters && config.enableDateFilter)
                const SizedBox(width: 8),
              if (config.enableDateFilter)
                Expanded(
                  child: DateRangeFilterWidget(
                    startDate: controller.startDate,
                    endDate: controller.endDate,
                    theme: controller.theme,
                    onRangeChanged: controller.setDateRange,
                    compact: true,
                    buttonHeight: 44,
                  ),
                ),
              if (config.enableDateFilter && config.enableSort && config.hasSorts)
                const SizedBox(width: 8),
              if (config.enableSort && config.hasSorts)
                Expanded(
                  child: SortWidget(
                    controller: controller,
                    options: config.sorts,
                    theme: controller.theme,
                    compact: true,
                    buttonHeight: 44,
                  ),
                ),
            ],
          ),

          // FilterStatusBar for clear filters + count
          const SizedBox(height: 8),
          FilterStatusBar(
            controller: controller,
            totalCount: totalCount,
            filteredCount: filteredCount,
          ),

          // Row 2: Search (full width with consistent height)
          if (config.enableSearch)
            SearchWidget(
              controller: controller,
              hintText: config.searchHint,
              theme: controller.theme,
              config: const SearchWidgetConfig(
                compact: false,
                buttonHeight: 44,
              ),
            ),
        ],
      ),
    );
  }


}

/// Compact version of FullFilterLayout for use in tight spaces
class CompactFullFilterLayout extends StatelessWidget {
  final FilterController controller;
  final ModuleFilterConfig config;
  final int? totalCount;
  final int? filteredCount;

  const CompactFullFilterLayout({
    Key? key,
    required this.controller,
    required this.config,
    this.totalCount,
    this.filteredCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FullFilterLayout(
      controller: controller,
      config: config,
      totalCount: totalCount,
      filteredCount: filteredCount,
      compact: true,
      padding: const EdgeInsets.all(4.0),
    );
  }
}

/// Minimal filter layout with only essential components
class MinimalFilterLayout extends StatelessWidget {
  final FilterController controller;
  final int? totalCount;
  final int? filteredCount;

  const MinimalFilterLayout({
    Key? key,
    required this.controller,
    this.totalCount,
    this.filteredCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(12.0),
      child: Column(
        children: [
          // Only date filter
          DateRangeFilterWidget(
            startDate: controller.startDate,
            endDate: controller.endDate,
            theme: controller.theme,
            onRangeChanged: controller.setDateRange,
            compact: true,
          ),

          // Filter status bar
          FilterStatusBar(
            controller: controller,
            totalCount: totalCount,
            filteredCount: filteredCount,
          ),
        ],
      ),
    );
  }
}
