import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../widgets/empty_state.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

class DailyTab extends StatefulWidget {
  final List<MilkRecordIsar> filteredRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final DateTime? selectedDate;
  final Function(BuildContext) selectDate;

  const DailyTab({
    Key? key,
    required this.filteredRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    this.selectedDate,
    required this.selectDate,
  }) : super(key: key);

  @override
  State<DailyTab> createState() => _DailyTabState();
}

class _DailyTabState extends State<DailyTab> {
  DateTime? _selectedDate;

  // Milk tab color palette - matching the main milk tab colors
  static const _primaryColor = Color(0xFF2E7D32); // Green (Daily tab color)
  static const _secondaryColor = Color(0xFF1976D2); // Blue (Morning)
  static const _accentColor = Color(0xFF7B1FA2); // Purple (Evening)
  static const _tealColor = Color(0xFF009688); // Teal (Average)
  static const _redColor = Color(0xFFD32F2F); // Red (Total)
  static const _cardShadowColor = Color(0x1A000000);
  static const _cardBorderRadius = 12.0; // Match milk tab border radius

  // Animation duration
  static const _animDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
  }

  @override
  void didUpdateWidget(covariant DailyTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != oldWidget.selectedDate) {
      _selectedDate = widget.selectedDate;
    }
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
    Color iconColor = Colors.white,
    Color textColor = Colors.black87,
    Color valueColor = Colors.black87,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and icon row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 4,
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor.withAlpha(204), // 0.8 * 255 = 204
                    fontWeight: FontWeight.w700,
                    fontSize: 12,
                    letterSpacing: 0.5,
                  ),
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(38), // 0.15 * 255 = 38
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: iconColor, size: 16),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Value with large font
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w900,
              color: valueColor,
              letterSpacing: -0.5,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: textColor.withAlpha(179), // 0.7 * 255 = 179
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCattleListItem(
    CattleIsar? cattle,
    double value,
    double percentage, {
    bool showTrend = true,
    Color progressColor = Colors.blue,
  }) {
    final greyColor = Colors.grey.shade600;
    final cattleName = cattle?.name ?? 'Unknown';
    final tagId = cattle?.tagId ?? '';
    final displayName =
        (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                cattle?.animalTypeId == 'cow' ? Icons.emoji_nature : Icons.pets,
                color: progressColor.withAlpha(179), // 0.7 * 255 = 179
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: Text(
                  displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  '${value.toStringAsFixed(1)} L',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.shade200,
                    color: progressColor,
                    minHeight: 5,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (showTrend)
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: percentage > 20 ? Colors.green : greyColor,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPieChart(
      Map<String, double> cattleTotals, double totalProduction) {
    // Sort cattle by production for the pie chart
    final sortedCattle = cattleTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Take top 5 cattle, group the rest as "Others"
    final pieData = <String, double>{};
    double othersValue = 0;

    for (int i = 0; i < sortedCattle.length; i++) {
      if (i < 5) {
        final cattle = widget.cattleMap[sortedCattle[i].key];
        final name = cattle?.name ?? 'Unknown';
        final tagId = cattle?.tagId ?? '';
        final shortName = name.length > 8 ? '${name.substring(0, 8)}...' : name;
        final displayName =
            tagId.isNotEmpty ? '$shortName ($tagId)' : shortName;
        pieData[displayName] = sortedCattle[i].value;
      } else {
        othersValue += sortedCattle[i].value;
      }
    }

    if (othersValue > 0) {
      pieData['Others'] = othersValue;
    }

    // Generate colors for pie sections - using milk tab color palette
    final colors = [
      _primaryColor, // Green
      _secondaryColor, // Blue
      _accentColor, // Purple
      _tealColor, // Teal
      _redColor, // Red
      const Color(0xFF3F51B5), // Indigo (avoiding orange, yellow, grey)
    ];

    // Create pie chart sections
    final sections = <PieChartSectionData>[];
    int colorIndex = 0;

    pieData.forEach((name, value) {
      final percentage = (value / totalProduction) * 100;
      sections.add(
        PieChartSectionData(
          color: colors[colorIndex % colors.length],
          value: value,
          title: '${percentage.toStringAsFixed(0)}%',
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
      colorIndex++;
    });

    return Column(
      children: [
        const SizedBox(height: 16),
        SizedBox(
          height: 250,
          child: PieChart(
            PieChartData(
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  // Handle touch response if needed
                },
              ),
              borderData: FlBorderData(show: false),
              sectionsSpace: 2,
              centerSpaceRadius: 40,
              sections: sections,
              startDegreeOffset: -90,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16,
          runSpacing: 8,
          alignment: WrapAlignment.center,
          children: [
            for (int i = 0; i < pieData.length; i++)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: colors[i % colors.length],
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    pieData.keys.elementAt(i),
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: colors[i % colors.length],
                    ),
                  ),
                ],
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCardWithHeader(
    String title,
    IconData icon,
    Widget content, {
    Color headerColor = Colors.black87,
    Color backgroundColor = Colors.white,
  }) {
    return Card(
      elevation: 4,
      shadowColor: _cardShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cardBorderRadius),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: headerColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(_cardBorderRadius),
                topRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: headerColor, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: headerColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(_cardBorderRadius),
                bottomRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildProductionDistributionCard(
    double totalMorning,
    double totalEvening,
    double totalProduction,
  ) {
    final morningPercentage = (totalMorning / totalProduction) * 100;
    final eveningPercentage = (totalEvening / totalProduction) * 100;

    final content = Row(
      children: [
        Expanded(
          child: _buildDistributionItem(
            'Morning',
            totalMorning,
            morningPercentage,
            Icons.wb_sunny_rounded, // Match milk tab icon
            _secondaryColor, // Blue for morning
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildDistributionItem(
            'Evening',
            totalEvening,
            eveningPercentage,
            Icons.nightlight_rounded, // Match milk tab icon
            _accentColor, // Purple for evening
          ),
        ),
      ],
    );

    return _buildCardWithHeader(
      'Production Distribution',
      Icons.pie_chart_rounded, // Match milk tab icon style
      content,
      headerColor: _primaryColor, // Use green for consistency
      backgroundColor: Colors.white,
    );
  }

  Widget _buildDistributionItem(
    String title,
    double value,
    double percentage,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                color: color,
                fontSize: 15,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          '${value.toStringAsFixed(1)} L',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w900,
            color: color,
          ),
        ),
        Text(
          '(${percentage.toStringAsFixed(1)}%)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color.withAlpha(204), // 0.8 * 255 = 204
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filteredRecords.isEmpty) {
      return const MilkEmptyState(
        icon: Icons.filter_alt_off_outlined,
        message: 'No Records Found',
        subtitle: 'No records found for the selected filters',
        color: _primaryColor,
      );
    }

    // Group records by date
    final recordsByDate = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        final date = DateTime(
          record.date!.year, 
          record.date!.month, 
          record.date!.day
        );
        recordsByDate.putIfAbsent(date, () => []).add(record);
      }
    }

    // Get the most recent date if no date is selected, otherwise use selected date
    final availableDates = recordsByDate.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    // If no date is selected or selected date has no records, use most recent date
    if (_selectedDate == null || !recordsByDate.containsKey(_selectedDate)) {
      _selectedDate =
          availableDates.isNotEmpty ? availableDates.first : DateTime.now();
    }

    // Get records for the selected date
    final dayRecords = recordsByDate[_selectedDate] ?? [];

    if (dayRecords.isEmpty) {
      return MilkEmptyState(
        icon: Icons.calendar_today_outlined,
        message: 'No Records for Selected Date',
        subtitle: 'No records found for ${DateFormat('MMMM d, y').format(_selectedDate!)}',
        color: _primaryColor,
        action: ElevatedButton.icon(
          onPressed: () => widget.selectDate(context),
          icon: const Icon(Icons.calendar_today),
          label: const Text('Select Another Date'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      );
    }

    // Calculate daily totals
    double totalMorning = 0;
    double totalEvening = 0;
    Map<String, double> cattleTotals = {};

    for (var record in dayRecords) {
      totalMorning += record.morningAmount ?? 0;
      totalEvening += record.eveningAmount ?? 0;
      // Track individual cattle totals
      final total = (record.morningAmount ?? 0) + (record.eveningAmount ?? 0);
      cattleTotals[record.cattleTagId ?? ''] = total;
    }

    final totalProduction = totalMorning + totalEvening;
    final averagePerCattle = totalProduction / dayRecords.length;

    // Sort cattle by production
    final sortedCattle = cattleTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Get top and bottom performers
    final top3Cattle = sortedCattle.take(3).toList();
    final bottom3Cattle = sortedCattle.reversed.take(3).toList();

    // Identify underperforming cattle (20% below average)
    final underperformingCattle = sortedCattle
        .where((entry) => entry.value < (averagePerCattle * 0.8))
        .toList();

    // Calculate trend compared to previous day if available
    String trendText = 'N/A';
    Color trendColor = Colors.grey;
    double trendPercentage = 0;

    if (availableDates.length > 1) {
      final previousDateIndex = availableDates.indexOf(_selectedDate!) + 1;
      if (previousDateIndex < availableDates.length) {
        final previousDate = availableDates[previousDateIndex];
        final previousRecords = recordsByDate[previousDate] ?? [];

        if (previousRecords.isNotEmpty) {
          double previousTotal = 0;
          for (var record in previousRecords) {
            previousTotal += (record.morningAmount ?? 0) + (record.eveningAmount ?? 0);
          }

          if (previousTotal > 0) {
            trendPercentage =
                ((totalProduction - previousTotal) / previousTotal) * 100;
            if (trendPercentage > 0) {
              trendText = '↑ ${trendPercentage.abs().toStringAsFixed(1)}%';
              trendColor = _secondaryColor; // Use green from color scheme
            } else if (trendPercentage < 0) {
              trendText = '↓ ${trendPercentage.abs().toStringAsFixed(1)}%';
              trendColor = _redColor; // Use red from color scheme
            } else {
              trendText = 'No change';
            }
          }
        }
      }
    }

    return AnimatedContainer(
      duration: _animDuration,
      color: Colors.grey.shade50,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with date selection
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(_cardBorderRadius),
                  boxShadow: const [
                    BoxShadow(
                      color: _cardShadowColor,
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Daily Analytics',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: _primaryColor,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.calendar_month,
                              color: _primaryColor),
                          onPressed: () => widget.selectDate(context),
                          style: IconButton.styleFrom(
                            backgroundColor: _primaryColor.withAlpha(26), // 0.1 * 255 = 26
                            padding: const EdgeInsets.all(8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      DateFormat('EEEE, MMMM d, yyyy').format(_selectedDate!),
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Key Stats Section
              Card(
                elevation: 4,
                shadowColor: _cardShadowColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_cardBorderRadius),
                ),
                child: Column(
                  children: [
                    // Header
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: _primaryColor.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(_cardBorderRadius),
                          topRight: Radius.circular(_cardBorderRadius),
                        ),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.analytics, color: _primaryColor, size: 18),
                          SizedBox(width: 8),
                          Text(
                            'Key Production Metrics',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: _primaryColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    // Content
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(_cardBorderRadius),
                          bottomRight: Radius.circular(_cardBorderRadius),
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildStatCard(
                                  icon: Icons.local_drink,
                                  iconColor: _primaryColor,
                                  valueColor: _primaryColor,
                                  title: 'TOTAL PRODUCTION',
                                  value:
                                      '${totalProduction.toStringAsFixed(1)} L',
                                  subtitle: 'Combined milk yield',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildStatCard(
                                  icon: Icons.equalizer,
                                  iconColor: trendColor,
                                  valueColor: trendColor,
                                  title: 'PRODUCTION TREND',
                                  value: trendText,
                                  subtitle: 'vs previous day',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildStatCard(
                                  icon: Icons.equalizer,
                                  iconColor: _secondaryColor,
                                  valueColor: _secondaryColor,
                                  title: 'AVERAGE PER COW',
                                  value:
                                      '${averagePerCattle.toStringAsFixed(1)} L',
                                  subtitle: '${dayRecords.length} cattle',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildStatCard(
                                  icon: Icons.star,
                                  iconColor: _accentColor,
                                  valueColor: _accentColor,
                                  title: 'TOP PRODUCER',
                                  value: top3Cattle.isNotEmpty
                                      ? '${top3Cattle.first.value.toStringAsFixed(1)} L'
                                      : 'N/A',
                                  subtitle: top3Cattle.isNotEmpty
                                      ? (() {
                                          final cattle = widget
                                              .cattleMap[top3Cattle.first.key];
                                          final cattleName =
                                              cattle?.name ?? 'Unknown';
                                          final tagId = cattle?.tagId ?? '';
                                          return (tagId.isNotEmpty)
                                              ? '$cattleName ($tagId)'
                                              : cattleName;
                                        })()
                                      : '',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Morning/Evening Distribution
              _buildProductionDistributionCard(
                totalMorning,
                totalEvening,
                totalProduction,
              ),
              const SizedBox(height: 16),

              // Top Performers Section
              _buildCardWithHeader(
                'Top Producers',
                Icons.trending_up,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...top3Cattle.map((entry) {
                      final cattle = widget.cattleMap[entry.key];
                      final percentage = (entry.value / totalProduction * 100);
                      return _buildCattleListItem(
                        cattle,
                        entry.value,
                        percentage,
                        progressColor: _secondaryColor, // Use green from color scheme
                      );
                    }).toList(),
                  ],
                ),
                headerColor: _secondaryColor, // Use green from color scheme
              ),
              const SizedBox(height: 16),

              // Low Performers Section
              _buildCardWithHeader(
                'Low Producers',
                Icons.trending_down,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...bottom3Cattle.map((entry) {
                      final cattle = widget.cattleMap[entry.key];
                      final percentage = (entry.value / totalProduction * 100);
                      return _buildCattleListItem(
                        cattle,
                        entry.value,
                        percentage,
                        progressColor: _redColor, // Use red instead of orange
                      );
                    }).toList(),
                  ],
                ),
                headerColor: _redColor, // Use red instead of orange
              ),
              const SizedBox(height: 16),

              // Underperforming Warnings Section
              _buildCardWithHeader(
                'Performance Alerts',
                Icons.warning_amber_rounded,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.info_outline, color: _redColor, size: 18),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Cattle producing below 80% of average',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    if (underperformingCattle.isNotEmpty)
                      ...underperformingCattle.map((entry) {
                        final cattle = widget.cattleMap[entry.key];
                        final percentage = (entry.value / averagePerCattle * 100);
                        return _buildCattleListItem(
                          cattle,
                          entry.value,
                          percentage,
                          showTrend: false,
                          progressColor: _redColor,
                        );
                      }).toList()
                    else
                      const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Text(
                          'No cattle are significantly underperforming today.',
                          style: TextStyle(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                  ],
                ),
                headerColor: _redColor,
              ),
              const SizedBox(height: 16),

              // Pie Chart Section - Moved to the end
              _buildCardWithHeader(
                'Production Distribution by Cattle',
                Icons.pie_chart,
                _buildPieChart(cattleTotals, totalProduction),
                headerColor: _secondaryColor,
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
