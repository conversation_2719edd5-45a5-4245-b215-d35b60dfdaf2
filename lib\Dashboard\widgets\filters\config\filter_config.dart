/// Type-safe configuration models for the universal filter system
///
/// These models follow the established enum-based pattern from DateRangeFilterWidget
/// and provide compile-time safety for filter configurations.

/// Enum for different filter input types
enum FilterInputType {
  dropdown,
  multiSelectChip,
}

/// Extension providing display properties for filter input types
extension FilterInputTypeExtension on FilterInputType {
  /// Display name for the input type
  String get displayName {
    switch (this) {
      case FilterInputType.dropdown:
        return 'Dropdown';
      case FilterInputType.multiSelectChip:
        return 'Multi-Select Chips';
    }
  }

  /// Whether this input type supports multiple selections
  bool get allowsMultipleSelection {
    switch (this) {
      case FilterInputType.dropdown:
        return false;
      case FilterInputType.multiSelectChip:
        return true;
    }
  }
}

/// Configuration for a single filter field with dynamic data support
class FilterField {
  final String key;
  final String label;
  final FilterInputType type;
  final List<String>? options; // Static options (optional)
  final Future<List<String>> Function(Map<String, dynamic>? dependencies)? optionsLoader; // Dynamic options loader
  final List<String>? dependsOn; // List of filter keys this field depends on
  final String? placeholder;
  final bool required;

  const FilterField({
    required this.key,
    required this.label,
    required this.type,
    this.options,
    this.optionsLoader,
    this.dependsOn,
    this.placeholder,
    this.required = false,
  });

  /// Create a dropdown filter field with static options
  const FilterField.dropdown({
    required String key,
    required String label,
    required List<String> options,
    String? placeholder,
    bool required = false,
  }) : this(
      key: key,
      label: label,
      type: FilterInputType.dropdown,
      options: options,
      placeholder: placeholder,
      required: required,
    );

  /// Create a dropdown filter field with dynamic options
  const FilterField.dynamicDropdown({
    required String key,
    required String label,
    required Future<List<String>> Function(Map<String, dynamic>? dependencies) optionsLoader,
    List<String>? dependsOn,
    String? placeholder,
    bool required = false,
  }) : this(
      key: key,
      label: label,
      type: FilterInputType.dropdown,
      optionsLoader: optionsLoader,
      dependsOn: dependsOn,
      placeholder: placeholder,
      required: required,
    );

  /// Create a multi-select chip filter field with static options
  const FilterField.multiSelectChip({
    required String key,
    required String label,
    required List<String> options,
    bool required = false,
  }) : this(
      key: key,
      label: label,
      type: FilterInputType.multiSelectChip,
      options: options,
      required: required,
    );

  /// Create a multi-select chip filter field with dynamic options
  const FilterField.dynamicMultiSelectChip({
    required String key,
    required String label,
    required Future<List<String>> Function(Map<String, dynamic>? dependencies) optionsLoader,
    List<String>? dependsOn,
    bool required = false,
  }) : this(
      key: key,
      label: label,
      type: FilterInputType.multiSelectChip,
      optionsLoader: optionsLoader,
      dependsOn: dependsOn,
      required: required,
    );

  /// Check if this field has dynamic options
  bool get isDynamic => optionsLoader != null;

  /// Check if this field has dependencies
  bool get hasDependencies => dependsOn != null && dependsOn!.isNotEmpty;

  /// Check if this field has valid options (either static or dynamic)
  bool get hasValidOptions => options != null || optionsLoader != null;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FilterField &&
          runtimeType == other.runtimeType &&
          key == other.key &&
          label == other.label &&
          type == other.type;

  @override
  int get hashCode => key.hashCode ^ label.hashCode ^ type.hashCode;

  @override
  String toString() => 'FilterField(key: $key, label: $label, type: $type)';
}

/// Configuration for a sort field
class SortField {
  final String key;
  final String label;
  final String? description;

  const SortField({
    required this.key,
    required this.label,
    this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SortField &&
          runtimeType == other.runtimeType &&
          key == other.key &&
          label == other.label;

  @override
  int get hashCode => key.hashCode ^ label.hashCode;

  @override
  String toString() => 'SortField(key: $key, label: $label)';
}

/// Complete filter configuration for a module
class ModuleFilterConfig {
  final List<FilterField> filters;
  final List<SortField> sorts;
  final String searchHint;
  final bool enableDateFilter;
  final bool enableSearch;
  final bool enableSort;

  const ModuleFilterConfig({
    this.filters = const [],
    this.sorts = const [],
    this.searchHint = 'Search...',
    this.enableDateFilter = true,
    this.enableSearch = true,
    this.enableSort = true,
  });

  /// Whether this configuration has any filter fields
  bool get hasFilters => filters.isNotEmpty;

  /// Whether this configuration has any sort fields
  bool get hasSorts => sorts.isNotEmpty;

  /// Whether this configuration has any interactive elements
  bool get hasAnyFeatures => 
      enableDateFilter || 
      enableSearch || 
      enableSort || 
      hasFilters;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModuleFilterConfig &&
          runtimeType == other.runtimeType &&
          filters == other.filters &&
          sorts == other.sorts &&
          searchHint == other.searchHint;

  @override
  int get hashCode => 
      filters.hashCode ^ 
      sorts.hashCode ^ 
      searchHint.hashCode;

  @override
  String toString() => 
      'ModuleFilterConfig(filters: ${filters.length}, sorts: ${sorts.length})';
}
