import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/breeding_report_data_isar.dart';
// Using only the local model classes instead of importing from external paths
// import '../../Breeding/models/breeding_record_isar.dart';
// import '../../Breeding/models/upcoming_delivery_isar.dart';

class BreedingSummaryTab extends StatelessWidget {
  final BreedingReportDataIsar reportData;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  BreedingSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.getSummaryData();
    final records = reportData.filteredRecords;
    final upcomingDeliveries = reportData.upcomingDeliveries;
    final chartData = reportData.getChartData();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: [
                _buildSummaryCard(
                  'Total Breedings',
                  summaryData['Total Breedings'].toString(),
                  Icons.event,
                ),
                _buildSummaryCard(
                  'Success Rate',
                  summaryData['Success Rate'].toString(),
                  Icons.check_circle,
                ),
                _buildSummaryCard(
                  'Total Cost',
                  currencyFormat.format(records.fold(0.0, (sum, record) => sum + record.cost)),
                  Icons.attach_money,
                ),
              ],
            ),
          ),
          if (records.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Breeding Status Distribution',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: PieChart(
                  PieChartData(
                    sections: _buildPieSections(),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Breeding Trend',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (chartData.isNotEmpty)
              _buildBreedingChart(chartData
                  .map((data) => FlSpot(
                      data.date!.millisecondsSinceEpoch.toDouble(), data.value ?? 0))
                  .toList()),
            if (upcomingDeliveries.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Upcoming Deliveries',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              _buildUpcomingDeliveriesSection(upcomingDeliveries),
            ],
          ] else
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32.0),
                child: Text(
                  'No breeding records available for the selected period',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 24, color: const Color(0xFF2E7D32)),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieSections() {
    final data = reportData.getSummaryData();
    final colors = [
      const Color(0xFF2E7D32), // Successful
      const Color(0xFFFFA000), // Pending
      const Color(0xFFD32F2F), // Failed
    ];

    final sections = <PieChartSectionData>[];

    void addSection(String key, Color color) {
      final value = data[key] as num? ?? 0;
      if (value > 0) {
        sections.add(
          PieChartSectionData(
            value: value.toDouble(),
            title: '$key\n${value.toInt()}',
            color: color,
            radius: 100,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );
      }
    }

    addSection('Successful', colors[0]);
    addSection('Pending', colors[1]);
    addSection('Failed', colors[2]);

    return sections;
  }

  Widget _buildUpcomingDeliveriesSection(List<UpcomingDeliveryIsar> deliveries) {
    // Group deliveries by time period
    final dueThisWeek = <UpcomingDeliveryIsar>[];
    final dueThisMonth = <UpcomingDeliveryIsar>[];
    final dueLater = <UpcomingDeliveryIsar>[];
    
    final now = DateTime.now();
    final oneWeek = now.add(const Duration(days: 7));
    final oneMonth = now.add(const Duration(days: 30));
    
    for (var delivery in deliveries) {
      if (delivery.expectedDate.isBefore(oneWeek)) {
        dueThisWeek.add(delivery);
      } else if (delivery.expectedDate.isBefore(oneMonth)) {
        dueThisMonth.add(delivery);
      } else {
        dueLater.add(delivery);
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (dueThisWeek.isNotEmpty)
            _buildDeliveryList(
              'Due This Week',
              dueThisWeek,
              Colors.red,
            ),
          if (dueThisMonth.isNotEmpty)
            _buildDeliveryList(
              'Due This Month',
              dueThisMonth,
              Colors.orange,
            ),
          if (dueLater.isNotEmpty)
            _buildDeliveryList(
              'Due Later',
              dueLater,
              Colors.green,
            ),
        ],
      ),
    );
  }

  Widget _buildDeliveryList(
      String title, List<UpcomingDeliveryIsar> deliveries, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: deliveries.length,
          itemBuilder: (context, index) {
            final delivery = deliveries[index];
            return Card(
              child: ListTile(
                title: Text('Cattle ID: ${delivery.cattleId}'),
                subtitle: Text(
                  'Expected Date: ${DateFormat('yyyy-MM-dd').format(delivery.expectedDate)}',
                ),
                leading: const Icon(Icons.calendar_today),
                trailing: Text(
                  '${delivery.daysRemaining} days',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildBreedingChart(List<FlSpot> chartData) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16.0),
      child: LineChart(
        LineChartData(
          gridData: const FlGridData(show: false),
          titlesData: FlTitlesData(
            leftTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final date =
                      DateTime.fromMillisecondsSinceEpoch(value.toInt());
                  return Text(
                    '${date.month}/${date.day}',
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: chartData,
              isCurved: true,
              color: Colors.blue,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                color: Colors.blue.withAlpha((0.1 * 255).round()),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
