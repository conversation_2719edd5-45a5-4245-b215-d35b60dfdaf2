import 'package:flutter/material.dart';
import '../models/weight_report_data_isar.dart';

class WeightDetailsTab extends StatelessWidget {
  final WeightReportDataIsar reportData;

  const WeightDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final records = reportData.filteredRecords;

    if (records.isEmpty) {
      return const Center(
        child: Text('No weight records found matching the selected criteria'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: reportData.getTableColumns(),
          rows: reportData.getTableRows(),
        ),
      ),
    );
  }
}
