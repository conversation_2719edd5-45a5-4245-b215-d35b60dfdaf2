import 'package:flutter/material.dart';

// OPTIMIZATION 3: Use a WidgetBuilder for lazy instantiation.
typedef TabContentBuilder = Widget Function(BuildContext context);

/// A data class to represent a tab item with its properties
class TabItem {
  final String label;
  final IconData icon;
  final Color? color; // Made optional for backward compatibility
  final TabContentBuilder? contentBuilder; // For self-managed mode
  final Widget? content; // For backward compatibility

  const TabItem({
    required this.label,
    required this.icon,
    this.color,
    this.contentBuilder,
    this.content,
  }) : assert(
          contentBuilder != null || content != null || color != null,
          'TabItem must have either contentBuilder, content, or color specified',
        );

  /// Creates a TabItem for externally controlled mode (color-based styling)
  const TabItem.controlled({
    required this.label,
    required this.icon,
    required Color this.color,
  }) : contentBuilder = null,
       content = null;

  /// Creates a TabItem for self-managed mode with lazy content loading
  const TabItem.withBuilder({
    required this.label,
    required this.icon,
    required this.contentBuilder,
    this.color,
  }) : content = null;

  /// Creates a TabItem for self-managed mode with immediate content
  const TabItem.withContent({
    required this.label,
    required this.icon,
    required this.content,
    this.color,
  }) : contentBuilder = null;
}

/// Tab configurations
class TabConfigurations {
  static const double defaultTabHeight = 48.0;
  static const EdgeInsets defaultPadding = EdgeInsets.symmetric(horizontal: 16.0);

  /// Configuration for a three-tab module (Analytics, Records, Insights)
  static List<TabItem> threeTabModule({
    required String tab1Label,
    required IconData tab1Icon,
    required String tab2Label,
    required IconData tab2Icon,
    required String tab3Label,
    required IconData tab3Icon,
  }) {
    return [
      TabItem.controlled(
        label: tab1Label,
        icon: tab1Icon,
        color: const Color(0xFF2E7D32), // Green for analytics
      ),
      TabItem.controlled(
        label: tab2Label,
        icon: tab2Icon,
        color: const Color(0xFF1976D2), // Blue for records
      ),
      TabItem.controlled(
        label: tab3Label,
        icon: tab3Icon,
        color: const Color(0xFF6A1B9A), // Purple for insights
      ),
    ];
  }

  /// Configuration for a two-tab detail screen (Analytics, Records)
  static List<TabItem> twoTabDetail({
    required String tab1Label,
    required IconData tab1Icon,
    required String tab2Label,
    required IconData tab2Icon,
  }) {
    return [
      TabItem.controlled(
        label: tab1Label,
        icon: tab1Icon,
        color: const Color(0xFF2E7D32), // Green for analytics
      ),
      TabItem.controlled(
        label: tab2Label,
        icon: tab2Icon,
        color: const Color(0xFF1976D2), // Blue for records
      ),
    ];
  }
}

/// A flexible, reusable tab bar widget.
///
/// It can operate in two modes:
/// 1.  **Default (Simple):** Manages its own TabController internally.
/// 2.  **Controlled:** Uses an external TabController provided by the parent.
class ReusableTabBar extends StatefulWidget {
  final List<TabItem> tabs;
  final int initialIndex;
  final Function(int index)? onTabChanged;

  // OPTIMIZATION 5: Add styling parameters for customization.
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final Color? indicatorColor;

  // OPTIMIZATION 1: Allow an external controller.
  final TabController? controller;

  /// Creates a self-managed tab bar.
  const ReusableTabBar({
    Key? key,
    required this.tabs,
    this.initialIndex = 0,
    this.onTabChanged,
    this.labelColor,
    this.unselectedLabelColor,
    this.indicatorColor,
  })  : controller = null, // Ensure controller is null in this constructor
        super(key: key);

  /// Creates a tab bar that is controlled by an external [TabController].
  ///
  /// This is useful when the tab state needs to be managed by a parent widget.
  const ReusableTabBar.controlled({
    Key? key,
    required this.tabs,
    required this.controller, // External controller is required here
    this.onTabChanged,
    this.labelColor,
    this.unselectedLabelColor,
    this.indicatorColor,
  })  : initialIndex = 0, // initialIndex is managed by the external controller
        super(key: key);

  @override
  State<ReusableTabBar> createState() => _ReusableTabBarState();
}
class _ReusableTabBarState extends State<ReusableTabBar>
    with SingleTickerProviderStateMixin {
  // This can be nullable now.
  TabController? _internalController;

  // A getter to simplify accessing the correct controller.
  TabController get _tabController =>
      widget.controller ?? _internalController!;

  @override
  void initState() {
    super.initState();
    // Only create an internal controller if an external one isn't provided.
    if (widget.controller == null) {
      _internalController = TabController(
        length: widget.tabs.length,
        vsync: this,
        initialIndex: widget.initialIndex,
      );
    }
    // Add the listener to whichever controller is active.
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      // Use the getter to ensure we're calling on the correct controller.
      widget.onTabChanged?.call(_tabController.index);
    }
  }

  // OPTIMIZATION 4: Handle dynamic changes to the tabs list.
  @override
  void didUpdateWidget(ReusableTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If we are using an internal controller and the number of tabs changes.
    if (widget.controller == null &&
        widget.tabs.length != oldWidget.tabs.length) {
      // Important: Must remove listener from the old controller before disposing.
      _internalController?.removeListener(_handleTabChange);
      _internalController?.dispose();

      _internalController = TabController(
        length: widget.tabs.length,
        vsync: this,
        // Try to preserve the index if it's still valid.
        initialIndex:
            _tabController.index.clamp(0, widget.tabs.length - 1),
      );
       _internalController!.addListener(_handleTabChange);
    }

    // If the controller provided by the parent changes.
    if (widget.controller != oldWidget.controller) {
       oldWidget.controller?.removeListener(_handleTabChange);
       widget.controller?.addListener(_handleTabChange);
    }
  }

  @override
  void dispose() {
    // Remove listener before disposing.
    // The external controller should be disposed by its owner.
    // We only dispose the one we created.
    _tabController.removeListener(_handleTabChange);
    _internalController?.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if this is self-managed mode (has content)
    final bool isSelfManaged = widget.tabs.any((tab) =>
        tab.contentBuilder != null || tab.content != null);

    if (isSelfManaged) {
      // Self-managed mode: Include TabBarView
      return Column(
        children: [
          Container(
            height: TabConfigurations.defaultTabHeight,
            decoration: BoxDecoration(
              color: theme.cardColor,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
              ),
            ),
            child: _buildTabBar(theme),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              // Use the builder to create content widgets lazily.
              children: widget.tabs.map((tab) {
                if (tab.contentBuilder != null) {
                  return tab.contentBuilder!(context);
                } else if (tab.content != null) {
                  return tab.content!;
                } else {
                  return const Center(
                    child: Text('No content provided for this tab'),
                  );
                }
              }).toList(),
            ),
          ),
        ],
      );
    } else {
      // Externally controlled mode: Only TabBar
      return Container(
        color: Colors.white,
        child: _buildTabBar(theme),
      );
    }
  }

  Widget _buildTabBar(ThemeData theme) {
    // Check if tabs have colors (externally controlled mode)
    final bool hasColors = widget.tabs.any((tab) => tab.color != null);

    if (hasColors) {
      // Color-based styling (externally controlled mode)
      return TabBar(
        controller: _tabController,
        labelColor: null, // We'll set colors per tab
        unselectedLabelColor: null, // We'll set colors per tab
        indicatorColor: _getIndicatorColor(),
        indicatorWeight: 3,
        tabs: widget.tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tabItem = entry.value;
          final isActive = _tabController.index == index;

          return Tab(
            icon: Icon(
              tabItem.icon,
              color: isActive
                  ? tabItem.color
                  : tabItem.color?.withAlpha(179), // 0.7 opacity
            ),
            child: Text(
              tabItem.label,
              style: TextStyle(
                color: isActive
                    ? tabItem.color
                    : tabItem.color?.withAlpha(179), // 0.7 opacity
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        }).toList(),
      );
    } else {
      // Theme-based styling (self-managed mode)
      return TabBar(
        controller: _tabController,
        tabs: widget.tabs.map((tab) {
          return Tab(
            icon: Icon(tab.icon, size: 20),
            text: tab.label,
          );
        }).toList(),
        labelColor: widget.labelColor ?? theme.primaryColor,
        unselectedLabelColor:
            widget.unselectedLabelColor ?? Colors.grey.shade600,
        indicatorColor: widget.indicatorColor ?? theme.primaryColor,
        indicatorWeight: 2,
      );
    }
  }

  /// Get the indicator color based on the selected tab
  Color _getIndicatorColor() {
    if (_tabController.index < widget.tabs.length) {
      final color = widget.tabs[_tabController.index].color;
      if (color != null) return color;
    }
    return const Color(0xFF2E7D32); // Default green
  }
}
