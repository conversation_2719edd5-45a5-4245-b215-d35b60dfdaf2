import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/weight_service.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../../services/database/database_helper.dart';

/// Centralized controller for weight management data
/// Eliminates redundant data fetching across tabs and provides single source of truth
class WeightController extends ChangeNotifier {
  final WeightService _weightService = WeightService();
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  late final CattleHandler _cattleHandler;

  // Core data
  List<WeightRecordIsar> _allWeightRecords = [];
  List<CattleIsar> _allCattle = [];
  Map<String, CattleIsar> _cattleMap = {};

  // Filtered data for records tab
  List<WeightRecordIsar> _filteredWeightRecords = [];
  
  // Filter states
  DateTime? _startDate;
  DateTime? _endDate;
  String _searchQuery = '';
  
  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  // Debounce timer for search
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 400);

  // Cached insights data
  AnalyticsSummary? _analyticsSummary;
  WeightInsightsData? _insightsData;

  // Getters
  List<WeightRecordIsar> get allWeightRecords => List.unmodifiable(_allWeightRecords);
  List<CattleIsar> get allCattle => List.unmodifiable(_allCattle);
  Map<String, CattleIsar> get cattleMap => Map.unmodifiable(_cattleMap);
  List<WeightRecordIsar> get filteredWeightRecords => List.unmodifiable(_filteredWeightRecords);
  
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String get searchQuery => _searchQuery;
  
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasData => _allWeightRecords.isNotEmpty;
  bool get hasError => _errorMessage != null;

  // Analytics and insights getters
  AnalyticsSummary get analyticsSummary => _analyticsSummary ?? _calculateAnalyticsSummary();
  WeightInsightsData get insightsData => _insightsData ?? _calculateInsightsData();

  WeightController() {
    _cattleHandler = _dbHelper.cattleHandler;
  }

  /// Initialize and load all data
  Future<void> initialize() async {
    await loadData();
  }

  /// Load all weight and cattle data from database
  Future<void> loadData() async {
    try {
      _setLoading(true);
      _clearError();

      // Load cattle data
      final cattle = await _cattleHandler.getAllCattle();
      final weightRecords = await _weightService.getAllWeightRecords();

      // Create cattle map for quick lookup
      final cattleMap = <String, CattleIsar>{};
      for (final animal in cattle) {
        if (animal.businessId != null) {
          cattleMap[animal.businessId!] = animal;
        }
      }

      _allCattle = cattle;
      _cattleMap = cattleMap;
      _allWeightRecords = weightRecords;
      _filteredWeightRecords = weightRecords;

      // Apply current filters
      _applyFilters();

      // Clear cached calculations to force recalculation
      _analyticsSummary = null;
      _insightsData = null;

      _setLoading(false);
    } catch (e) {
      _setError('Error loading weight data: $e');
      _setLoading(false);
    }
  }

  /// Refresh data from database
  Future<void> refresh() async {
    await loadData();
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    if (_startDate != startDate || _endDate != endDate) {
      _startDate = startDate;
      _endDate = endDate;
      _applyFilters();
      // Clear cached calculations when filters change
      _analyticsSummary = null;
      _insightsData = null;
      notifyListeners();
    }
  }

  /// Set search query with debouncing
  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      
      // Cancel previous timer
      _searchDebounceTimer?.cancel();
      
      // Set new timer for debounced search
      _searchDebounceTimer = Timer(_searchDebounceDelay, () {
        _applyFilters();
        notifyListeners();
      });
    }
  }

  /// Apply all active filters to weight records
  void _applyFilters() {
    List<WeightRecordIsar> filtered = List.from(_allWeightRecords);

    // Apply date range filter
    if (_startDate != null && _endDate != null) {
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      filtered = filtered.where((record) {
        final date = record.measurementDate;
        if (date == null) return false;
        return !date.isBefore(_startDate!) && date.isBefore(inclusiveEndDate);
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final searchLower = _searchQuery.toLowerCase();
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleBusinessId];
        if (cattle == null) return false;
        
        final name = cattle.name?.toLowerCase() ?? '';
        final tagId = cattle.tagId?.toLowerCase() ?? '';
        final businessId = cattle.businessId?.toLowerCase() ?? '';
        
        return name.contains(searchLower) || 
               tagId.contains(searchLower) || 
               businessId.contains(searchLower);
      }).toList();
    }

    _filteredWeightRecords = filtered;
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _searchQuery = '';
    _searchDebounceTimer?.cancel();
    _applyFilters();
    notifyListeners();
  }

  /// Get filtered weight records for analytics (with date range)
  List<WeightRecordIsar> getAnalyticsData() {
    if (_startDate == null || _endDate == null) {
      return _allWeightRecords;
    }
    
    final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
    return _allWeightRecords.where((record) {
      final date = record.measurementDate;
      if (date == null) return false;
      return !date.isBefore(_startDate!) && date.isBefore(inclusiveEndDate);
    }).toList();
  }

  /// Get weight records for a specific cattle
  List<WeightRecordIsar> getRecordsForCattle(String cattleBusinessId) {
    return _allWeightRecords
        .where((record) => record.cattleBusinessId == cattleBusinessId)
        .toList();
  }

  /// Get cattle by business ID
  CattleIsar? getCattle(String businessId) {
    return _cattleMap[businessId];
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Calculate analytics summary for the analytics tab
  AnalyticsSummary _calculateAnalyticsSummary() {
    if (_analyticsSummary != null) return _analyticsSummary!;

    final records = getAnalyticsData();
    if (records.isEmpty) return AnalyticsSummary.empty;

    final totalRecords = records.length;
    final totalCattle = _cattleMap.length;
    final totalWeight = records.fold<double>(0.0, (sum, record) => sum + record.weight);
    final averageWeight = totalWeight / totalRecords;

    // Find earliest and latest records
    DateTime? earliest, latest;
    for (final record in records) {
      if (record.measurementDate != null) {
        if (earliest == null || record.measurementDate!.isBefore(earliest)) {
          earliest = record.measurementDate;
        }
        if (latest == null || record.measurementDate!.isAfter(latest)) {
          latest = record.measurementDate;
        }
      }
    }

    _analyticsSummary = AnalyticsSummary(
      totalRecords: totalRecords,
      totalCattle: totalCattle,
      averageWeight: averageWeight,
      totalWeight: totalWeight,
      earliestRecord: earliest,
      latestRecord: latest,
    );

    return _analyticsSummary!;
  }

  /// Calculate complete insights data for the insights tab
  WeightInsightsData _calculateInsightsData() {
    if (_insightsData != null) return _insightsData!;

    final records = getAnalyticsData();
    if (records.isEmpty) return WeightInsightsData.empty;

    final performance = _calculatePerformanceInsights(records);
    final health = _calculateHealthInsights(records);
    final trends = _calculateTrendInsights(records);
    final recommendations = _generateRecommendations(performance, health, trends);

    _insightsData = WeightInsightsData(
      performance: performance,
      health: health,
      trends: trends,
      recommendations: recommendations,
      lastUpdated: DateTime.now(),
    );

    return _insightsData!;
  }

  /// Calculate performance insights
  PerformanceInsights _calculatePerformanceInsights(List<WeightRecordIsar> records) {
    if (records.isEmpty) return PerformanceInsights.empty;

    // Group records by cattle
    final Map<String, List<WeightRecordIsar>> cattleRecords = {};
    for (final record in records) {
      final cattleId = record.cattleBusinessId;
      if (cattleId != null) {
        cattleRecords.putIfAbsent(cattleId, () => []).add(record);
      }
    }

    // Calculate daily gain for each cattle
    final Map<String, double> dailyGains = {};
    String topPerformerId = '';
    String topPerformerName = 'No data';
    double topPerformerGainRate = 0.0;
    int gainingCattle = 0;
    int losingCattle = 0;

    for (final entry in cattleRecords.entries) {
      final cattleId = entry.key;
      final cattleRecordsList = entry.value;

      if (cattleRecordsList.length < 2) continue;

      // Sort by date
      cattleRecordsList.sort((a, b) =>
        (a.measurementDate ?? DateTime.now()).compareTo(b.measurementDate ?? DateTime.now()));

      final firstRecord = cattleRecordsList.first;
      final lastRecord = cattleRecordsList.last;

      if (firstRecord.measurementDate != null && lastRecord.measurementDate != null) {
        final daysDiff = lastRecord.measurementDate!.difference(firstRecord.measurementDate!).inDays;
        if (daysDiff > 0) {
          final weightDiff = lastRecord.weight - firstRecord.weight;
          final dailyGain = weightDiff / daysDiff;
          dailyGains[cattleId] = dailyGain;

          if (dailyGain > 0) {
            gainingCattle++;
          } else if (dailyGain < 0) {
            losingCattle++;
          }

          if (dailyGain > topPerformerGainRate) {
            topPerformerGainRate = dailyGain;
            topPerformerId = cattleId;
            final cattle = _cattleMap[cattleId];
            topPerformerName = cattle?.name ?? 'Unknown';
          }
        }
      }
    }

    final averageDailyGain = dailyGains.values.isNotEmpty
        ? dailyGains.values.reduce((a, b) => a + b) / dailyGains.values.length
        : 0.0;

    String growthTrend = 'Stable';
    if (averageDailyGain > 0.5) {
      growthTrend = 'Excellent';
    } else if (averageDailyGain > 0.2) {
      growthTrend = 'Good';
    } else if (averageDailyGain < -0.1) {
      growthTrend = 'Declining';
    }

    return PerformanceInsights(
      topPerformerName: topPerformerName,
      topPerformerId: topPerformerId,
      topPerformerGainRate: topPerformerGainRate,
      averageDailyGain: averageDailyGain,
      growthTrend: growthTrend,
      growthTrendDescription: _getGrowthTrendDescription(growthTrend),
      totalGainingCattle: gainingCattle,
      totalLosingCattle: losingCattle,
    );
  }

  /// Calculate health insights
  HealthInsights _calculateHealthInsights(List<WeightRecordIsar> records) {
    if (records.isEmpty) return HealthInsights.empty;

    // Calculate average body condition (simplified - using weight ranges)
    final weights = records.map((r) => r.weight).toList();
    final avgWeight = weights.reduce((a, b) => a + b) / weights.length;
    final avgBodyCondition = _estimateBodyConditionFromWeight(avgWeight);

    // Calculate weight consistency (coefficient of variation)
    final variance = weights.map((w) => pow(w - avgWeight, 2)).reduce((a, b) => a + b) / weights.length;
    final stdDev = sqrt(variance);
    final coefficientOfVariation = stdDev / avgWeight;

    String consistency = 'Good';
    if (coefficientOfVariation > 0.2) {
      consistency = 'Poor';
    } else if (coefficientOfVariation > 0.1) {
      consistency = 'Fair';
    } else {
      consistency = 'Excellent';
    }

    // Health alerts (simplified logic)
    int alerts = 0;
    final alertReasons = <String>[];

    // Check for extreme weights
    final minWeight = weights.reduce(min);
    final maxWeight = weights.reduce(max);
    if (maxWeight - minWeight > avgWeight * 0.5) {
      alerts++;
      alertReasons.add('High weight variation detected');
    }

    return HealthInsights(
      averageBodyCondition: avgBodyCondition,
      consistencyRating: consistency,
      consistencyDescription: _getConsistencyDescription(consistency),
      healthAlerts: alerts,
      alertReasons: alertReasons,
      underweightCattle: 0, // Simplified
      overweightCattle: 0,  // Simplified
      normalWeightCattle: _cattleMap.length,
    );
  }

  /// Calculate trend insights
  TrendInsights _calculateTrendInsights(List<WeightRecordIsar> records) {
    if (records.isEmpty) return TrendInsights.empty;

    // Simple trend calculation
    final sortedRecords = List<WeightRecordIsar>.from(records);
    sortedRecords.sort((a, b) =>
      (a.measurementDate ?? DateTime.now()).compareTo(b.measurementDate ?? DateTime.now()));

    if (sortedRecords.length < 2) return TrendInsights.empty;

    final firstWeight = sortedRecords.first.weight;
    final lastWeight = sortedRecords.last.weight;
    final weightChange = lastWeight - firstWeight;
    final percentChange = (weightChange / firstWeight) * 100;

    String trend = 'Stable';
    if (percentChange > 5) {
      trend = 'Increasing';
    } else if (percentChange < -5) {
      trend = 'Decreasing';
    }

    return TrendInsights(
      overallTrend: trend,
      trendDescription: _getTrendDescription(trend, percentChange),
      trendPercentage: percentChange,
      seasonalPattern: 'No pattern', // Simplified
      seasonalDescription: 'Insufficient data for seasonal analysis',
      monthlyTrends: [], // Simplified
      recommendation: _getTrendRecommendation(trend),
    );
  }

  /// Generate recommendations based on insights
  List<WeightRecommendation> _generateRecommendations(
    PerformanceInsights performance,
    HealthInsights health,
    TrendInsights trends,
  ) {
    final recommendations = <WeightRecommendation>[];

    // Performance-based recommendations
    if (performance.averageDailyGain < 0.1) {
      recommendations.add(const WeightRecommendation(
        title: 'Improve Nutrition Program',
        description: 'Low average daily gain indicates potential nutrition deficiencies.',
        priority: 'High',
        category: 'Nutrition',
        actionItems: [
          'Review feed quality and quantity',
          'Consider supplemental feeding',
          'Consult with nutritionist',
        ],
      ));
    }

    // Health-based recommendations
    if (health.healthAlerts > 0) {
      recommendations.add(const WeightRecommendation(
        title: 'Monitor Cattle Health',
        description: 'Health alerts detected requiring attention.',
        priority: 'High',
        category: 'Health',
        actionItems: [
          'Schedule veterinary examination',
          'Review health protocols',
          'Monitor affected cattle closely',
        ],
      ));
    }

    // Trend-based recommendations
    if (trends.overallTrend == 'Decreasing') {
      recommendations.add(const WeightRecommendation(
        title: 'Address Weight Loss Trend',
        description: 'Overall weight trend is declining across the herd.',
        priority: 'High',
        category: 'Management',
        actionItems: [
          'Investigate potential causes',
          'Review feeding schedule',
          'Check for disease or parasites',
        ],
      ));
    }

    return recommendations;
  }

  // Helper methods for descriptions
  String _getGrowthTrendDescription(String trend) {
    switch (trend) {
      case 'Excellent': return 'Exceptional growth performance';
      case 'Good': return 'Above average growth rates';
      case 'Declining': return 'Below average performance needs attention';
      default: return 'Moderate growth performance';
    }
  }

  String _getConsistencyDescription(String consistency) {
    switch (consistency) {
      case 'Excellent': return 'Very consistent measurements';
      case 'Good': return 'Reasonably consistent data';
      case 'Fair': return 'Some variation in measurements';
      default: return 'High variation in measurements';
    }
  }

  String _getTrendDescription(String trend, double percentage) {
    switch (trend) {
      case 'Increasing': return 'Weight trending upward by ${percentage.toStringAsFixed(1)}%';
      case 'Decreasing': return 'Weight trending downward by ${percentage.abs().toStringAsFixed(1)}%';
      default: return 'Weight remaining relatively stable';
    }
  }

  String _getTrendRecommendation(String trend) {
    switch (trend) {
      case 'Increasing': return 'Continue current management practices';
      case 'Decreasing': return 'Review and adjust feeding program';
      default: return 'Monitor for changes and maintain consistency';
    }
  }

  double _estimateBodyConditionFromWeight(double weight) {
    // Simplified body condition estimation
    // In reality, this would be more complex and breed-specific
    if (weight < 300) return 2.5;
    if (weight < 400) return 3.0;
    if (weight < 500) return 3.5;
    if (weight < 600) return 4.0;
    return 4.5;
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }
}
