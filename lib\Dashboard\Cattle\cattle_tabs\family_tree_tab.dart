import 'package:flutter/material.dart';
import '../models/cattle_isar.dart';
import '../../../utils/navigation_utils.dart';

// Prominent colors following app color rules (no orange, yellow, grey, and no color used twice)
class FamilyTreeColors {
  static const Color motherColor = Color(0xFF2196F3);      // Blue - for mother section
  static const Color siblingColor = Color(0xFF8BC34A);     // Light Green - for siblings section
  static const Color calfColor = Color(0xFF4CAF50);        // Green - for calves section
  static const Color maleColor = Color(0xFF3F51B5);        // Indigo - for male gender icons
  static const Color femaleColor = Color(0xFFE91E63);      // Pink - for female gender icons
  static const Color relationshipLabelColor = Color(0xFF673AB7); // Deep Purple - for relationship labels
  static const Color healthyColor = Color(0xFF00C853);     // Bright Green - for healthy status
  static const Color sickColor = Color(0xFFF44336);        // Red - for sick status
  static const Color recoveringColor = Color(0xFF00BCD4);  // Cyan - for recovering status
  static const Color pregnantColor = Color(0xFF9C27B0);    // Purple - for pregnant status
  static const Color ageColor = Color(0xFF1E88E5);         // Blue - for age indicators
  static const Color weightColor = Color(0xFF607D8B);      // Blue Grey - for weight indicators
}

class FamilyTreeTab extends StatelessWidget {
  final CattleIsar cattle;
  final CattleIsar? motherCattle;
  final List<CattleIsar> calves;
  final List<CattleIsar> siblings;

  const FamilyTreeTab({
    Key? key,
    required this.cattle,
    this.motherCattle,
    required this.calves,
    required this.siblings,
  }) : super(key: key);

  // Helper method to get siblings
  List<CattleIsar> _getSiblings() {
    // Simply return the provided siblings list
    return siblings;
  }

  // Helper method to format age
  String _calculateAge(DateTime? birthDate) {
    if (birthDate == null) return 'Unknown';

    final now = DateTime.now();
    final difference = now.difference(birthDate);

    final years = difference.inDays ~/ 365;
    final months = (difference.inDays % 365) ~/ 30;
    final days = (difference.inDays % 365) % 30;

    if (years > 0) {
      return '$years ${years == 1 ? 'year' : 'years'}';
    } else if (months > 0) {
      return '$months ${months == 1 ? 'month' : 'months'}';
    } else {
      return '$days ${days == 1 ? 'day' : 'days'}';
    }
  }

  // Helper method to get health status color
  Color _getHealthStatusColor(String? status, ThemeData theme) {
    if (status == null) return FamilyTreeColors.healthyColor.withValues(alpha: 0.4);

    switch (status.toLowerCase()) {
      case 'healthy':
        return FamilyTreeColors.healthyColor;
      case 'sick':
        return FamilyTreeColors.sickColor;
      case 'recovering':
        return FamilyTreeColors.recoveringColor;
      case 'pregnant':
        return FamilyTreeColors.pregnantColor;
      default:
        return FamilyTreeColors.healthyColor.withValues(alpha: 0.4);
    }
  }

  // Helper method to get health status icon
  IconData _getHealthStatusIcon(String? status) {
    if (status == null) return Icons.help_outline;

    switch (status.toLowerCase()) {
      case 'healthy':
        return Icons.check_circle_outline;
      case 'sick':
        return Icons.sick_outlined;
      case 'recovering':
        return Icons.healing_outlined;
      case 'pregnant':
        return Icons.pregnant_woman_outlined;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    // Get calculated siblings
    final List<CattleIsar> calculatedSiblings = _getSiblings();

    final bool hasRelationships = motherCattle != null ||
        calves.isNotEmpty ||
        calculatedSiblings.isNotEmpty;

    return Padding(
      padding: const EdgeInsets.only(
          bottom: 80), // Add bottom padding to avoid FAB overlap
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: hasRelationships
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Mother section
                  if (motherCattle != null) ...[
                    _buildRelationshipCard(
                      context: context,
                      theme: theme,
                      title: 'Mother',
                      count: 1,
                      icon: Icons.arrow_upward,
                      iconColor: FamilyTreeColors.motherColor,
                      relatives: [motherCattle!],
                      relationship: 'Mother',
                    ),
                    const SizedBox(height: 16),
                  ],
                  // Siblings section - use calculated siblings
                  if (calculatedSiblings.isNotEmpty) ...[
                    _buildRelationshipCard(
                      context: context,
                      theme: theme,
                      title: 'Siblings (${calculatedSiblings.length})',
                      icon: Icons.compare_arrows,
                      iconColor: FamilyTreeColors.siblingColor,
                      relatives: calculatedSiblings,
                      relationship: 'Sibling',
                    ),
                    const SizedBox(height: 16),
                  ],
                  // Calves section
                  if (calves.isNotEmpty) ...[
                    _buildRelationshipCard(
                      context: context,
                      theme: theme,
                      title: 'Calves (${calves.length})',
                      icon: Icons.arrow_downward,
                      iconColor: FamilyTreeColors.calfColor,
                      relatives: calves,
                      relationship: 'Calf',
                    ),
                  ],
                ],
              )
            : _buildEmptyState(theme),
      ),
    );
  }

  Widget _buildRelationshipCard({
    required BuildContext context,
    required ThemeData theme,
    required String title,
    int? count,
    required IconData icon,
    required Color iconColor,
    required List<CattleIsar> relatives,
    required String relationship,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: iconColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: iconColor.withAlpha(51), // 0.2 * 255 = 51
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.titleLarge
                        ?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                  ),
                ),
                if (count != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: iconColor.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      count.toString(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: iconColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: relatives.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final relative = relatives[index];
              return _buildRelativeListItem(
                  context, theme, relative, relationship);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRelativeListItem(BuildContext context, ThemeData theme,
      CattleIsar relative, String relationship) {
    final bool isMale = (relative.gender ?? '').toLowerCase() == 'male';
    final Color genderColor = isMale
        ? FamilyTreeColors.maleColor
        : FamilyTreeColors.femaleColor;
    final IconData genderIcon = isMale ? Icons.male : Icons.female;

    // Calculate age if date of birth is available
    final String age = relative.dateOfBirth != null
        ? _calculateAge(relative.dateOfBirth)
        : 'Unknown age';

    return InkWell(
      onTap: () {
        final tagId = relative.tagId;
        if (tagId != null && tagId.isNotEmpty) {
          navigateToCattleDetails(context, tagId);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Cannot navigate: Tag ID missing for ${relative.name ?? 'relative'}')),
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: genderColor.withAlpha(51), // 0.2 * 255 = 51
              child: Icon(
                genderIcon,
                color: genderColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${relative.name ?? 'Unknown'} (${relative.tagId ?? 'No Tag'})',
                          style: theme.textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: genderColor.withAlpha(26), // 0.1 * 255 = 26
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          relative.gender ?? '',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: genderColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // Age and relationship row
                  Row(
                    children: [
                      // Age info with colored icon
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: FamilyTreeColors.ageColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.cake_rounded,
                              size: 14,
                              color: FamilyTreeColors.ageColor,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            age,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: FamilyTreeColors.ageColor,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(), // Push relationship to the right
                      // Relationship tag (with unique color)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: FamilyTreeColors.relationshipLabelColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          relationship,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: FamilyTreeColors.relationshipLabelColor,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Health status and weight row
                  if ((relative.healthInfo?.status != null &&
                          relative.healthInfo!.status!.isNotEmpty) ||
                      (relationship == 'Calf' && relative.weight != null))
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Row(
                        children: [
                          // Health status with colored icon
                          if (relative.healthInfo?.status != null &&
                              relative.healthInfo!.status!.isNotEmpty)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: _getHealthStatusColor(
                                            relative.healthInfo!.status, theme)
                                        .withAlpha(20),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Icon(
                                    _getHealthStatusIcon(
                                        relative.healthInfo!.status),
                                    size: 14,
                                    color: _getHealthStatusColor(
                                        relative.healthInfo!.status, theme),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  relative.healthInfo!.status!,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: _getHealthStatusColor(
                                        relative.healthInfo!.status, theme),
                                  ),
                                ),
                              ],
                            ),

                          // Add spacer if both health and weight are shown
                          if (relative.healthInfo?.status != null &&
                              relative.healthInfo!.status!.isNotEmpty &&
                              relationship == 'Calf' &&
                              relative.weight != null)
                            const Spacer(),

                          // Weight with colored icon for calves
                          if (relationship == 'Calf' && relative.weight != null)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: FamilyTreeColors.weightColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Icon(
                                    Icons.monitor_weight_rounded,
                                    size: 14,
                                    color: FamilyTreeColors.weightColor,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${relative.weight} kg',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: FamilyTreeColors.weightColor,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: FamilyTreeColors.motherColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.family_restroom,
                  size: 48,
                  color: FamilyTreeColors.motherColor,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No Family Relationships',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'This cattle has no recorded mother, siblings, or calves in the system.',
                style: TextStyle(
                  fontSize: 14,
                  color: FamilyTreeColors.motherColor.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
