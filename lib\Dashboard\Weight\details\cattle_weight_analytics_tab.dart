import 'package:flutter/material.dart';
import '../controllers/cattle_weight_detail_controller.dart';
import '../../widgets/index.dart';

class CattleWeightAnalyticsTab extends StatefulWidget {
  final CattleWeightDetailController controller;

  const CattleWeightAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<CattleWeightAnalyticsTab> createState() => _CattleWeightAnalyticsTabState();
}

class _CattleWeightAnalyticsTabState extends State<CattleWeightAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (!widget.controller.hasData) {
          return EmptyState.custom(
            icon: Icons.analytics,
            title: 'No Weight Data',
            message: 'Add weight records for ${widget.controller.cattle.name ?? 'this cattle'} to see analytics.',
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DateRangeFilterWidget(
                startDate: widget.controller.startDate,
                endDate: widget.controller.endDate,
                theme: DateRangeTheme.weight,
                onRangeChanged: (start, end) {
                  widget.controller.setDateRange(start, end);
                },
                compact: true,
              ),
              const SizedBox(height: 16),
              _buildCattleInfoCard(),
              const SizedBox(height: 16),
              _buildWeightSummaryCard(),
              const SizedBox(height: 16),
              _buildWeightTrendCard(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCattleInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pets, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  widget.controller.cattle.name ?? 'Unknown Cattle',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Tag ID', widget.controller.cattle.tagId ?? 'N/A'),
                ),
                Expanded(
                  child: _buildInfoItem('Gender', widget.controller.cattle.gender ?? 'N/A'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Age', _calculateAge()),
                ),
                Expanded(
                  child: _buildInfoItem('Records', widget.controller.analyticsSummary.totalRecords.toString()),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildWeightSummaryCard() {
    final analyticsSummary = widget.controller.analyticsSummary;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                const Icon(Icons.monitor_weight, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Weight Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Current Weight',
                    '${analyticsSummary.currentWeight.toStringAsFixed(1)} kg',
                    Icons.scale,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Average Weight',
                    '${analyticsSummary.averageWeight.toStringAsFixed(1)} kg',
                    Icons.analytics,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Weight Gain',
                    '${analyticsSummary.weightGain >= 0 ? '+' : ''}${analyticsSummary.weightGain.toStringAsFixed(1)} kg',
                    analyticsSummary.weightGain >= 0 ? Icons.trending_up : Icons.trending_down,
                    analyticsSummary.weightGain >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Avg Daily Gain',
                    '${analyticsSummary.averageDailyGain.toStringAsFixed(2)} kg/day',
                    Icons.speed,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWeightTrendCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.show_chart, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'Weight Trend',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text(
                  'Weight trend chart will be displayed here',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _calculateAge() {
    if (widget.controller.cattle.dateOfBirth == null) return 'N/A';

    final now = DateTime.now();
    final age = now.difference(widget.controller.cattle.dateOfBirth!);
    final years = age.inDays ~/ 365;
    final months = (age.inDays % 365) ~/ 30;

    if (years > 0) {
      return '$years years, $months months';
    } else {
      return '$months months';
    }
  }


}
