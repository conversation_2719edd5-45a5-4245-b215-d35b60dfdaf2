import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart'; // Add for generating unique keys if needed

import '../../Events/models/event_isar.dart';
import '../services/cattle_event_service.dart'; // Ensure this path is correct
import '../../Events/dialogs/event_form_dialog.dart';
import '../../../services/database/isar_service.dart';
import '../models/cattle_isar.dart'; // Added import
import '../../../utils/message_utils.dart';
// import 'package:isar/isar.dart'; // Removed unused import

class EventsTab extends StatefulWidget {
  // final dynamic cattle; // Changed to CattleIsar
  final CattleIsar cattle;

  const EventsTab({Key? key, required this.cattle}) : super(key: key);

  @override
  State<EventsTab> createState() => _EventsTabState();
}

class _EventsTabState extends State<EventsTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late final CattleEventService _eventService;
  List<EventIsar> _pastEvents = [];
  List<EventIsar> _upcomingEvents = [];
  List<EventIsar> _missedEvents = [];
  bool _isLoading = true;
  bool _isMounted = false; // Track mounted state

  // Helper for sorting events (descending by due date or event date)
  int _sortEvents(EventIsar a, EventIsar b) {
    final dateA =
        a.dueDate ?? a.eventDate ?? DateTime(1970); // Use epoch if no date
    final dateB = b.dueDate ?? b.eventDate ?? DateTime(1970);
    return dateB.compareTo(dateA); // Descending
  }

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    _tabController = TabController(length: 3, vsync: this);

    // --- Robust Service Initialization ---
    // Assume GetIt setup is guaranteed elsewhere in the app initialization
    try {
      final isarService = GetIt.instance<IsarService>();
      if (!isarService.isInitialized) {
        // Handle case where IsarService itself isn't fully initialized yet
        // This might involve showing an error or waiting. For now, log and prevent loading.
        debugPrint("EventsTab initState: IsarService not yet initialized.");
        _isLoading = false; // Prevent loading state if service isn't ready
        // Optionally, set an error state here
        return; // Exit initState if service is bad
      }
      _eventService = CattleEventService(isarService.isar);
      _loadEvents();
    } catch (e, s) {
      // This catch block now signifies a critical GetIt setup failure
      debugPrint(
          'FATAL: Error getting IsarService via GetIt in EventsTab: $e\n$s');
      _isLoading = false; // Prevent loading state
      // Consider navigating to an error screen or showing a persistent error message
      // For now, we just log and the tab will likely show empty/error state.
      // Initialize _eventService with a dummy to prevent null errors later,
      // though operations will fail.
      // _eventService = CattleEventService(null); // Or handle differently
    }
  }

  // --- Removed didChangeDependencies ---
  // @override
  // void didChangeDependencies() { ... }

  @override
  void dispose() {
    _isMounted = false;
    _tabController.dispose();
    super.dispose();
  }

  // Safely update state only if widget is mounted
  void _safeSetState(VoidCallback fn) {
    if (_isMounted) {
      setState(fn);
    }
  }

  // Show a user-friendly SnackBar - Deprecated, use MessageUtils instead
  void _showSnackBar(String message, {bool isError = false}) {
    if (!_isMounted) return; // Don't show if not mounted

    if (isError) {
      MessageUtils.showError(context, message);
    } else {
      MessageUtils.showSuccess(context, message);
    }
  }

  Future<void> _loadEvents() async {
    if (!_isMounted) return; // Check mounted state
    _safeSetState(() {
      _isLoading = true;
    });

    try {
      // Ensure _eventService is initialized before using it
      // This check is important if initState failed to get a valid service
      if (!GetIt.instance.isRegistered<IsarService>() ||
          !GetIt.instance<IsarService>().isInitialized) {
        throw Exception("IsarService not available");
      }

      final allEvents = await _eventService.getEventsForCattle(
          widget.cattle.businessId ?? ''); // Use null check for safety

      if (!_isMounted) return; // Check again after async gap

      // --- Clearer Event Categorization (Option A: Mutually Exclusive) ---
      final now = DateTime.now();
      final today =
          DateTime(now.year, now.month, now.day); // For comparing dates only

      List<EventIsar> pastEvents = [];
      List<EventIsar> upcomingEvents = [];
      List<EventIsar> missedEvents = [];

      for (var event in allEvents) {
        final dueDate = event.dueDate;
        // Consider events due 'today' as upcoming, not past due yet.
        final isStrictlyPastDue = dueDate != null && dueDate.isBefore(today);

        if (event.isCompleted) {
          pastEvents.add(event);
        } else if (event.isMissed || isStrictlyPastDue) {
          missedEvents.add(event);
        } else {
          // Upcoming: Not completed, not missed, and not strictly past due
          upcomingEvents.add(event);
        }
      }

      // Sort lists
      pastEvents.sort(_sortEvents);
      upcomingEvents.sort(_sortEvents);
      missedEvents.sort(_sortEvents);

      _safeSetState(() {
        _pastEvents = pastEvents;
        _upcomingEvents = upcomingEvents;
        _missedEvents = missedEvents;
        _isLoading = false;
      });
    } catch (e, s) {
      debugPrint('Error loading events: $e\n$s');
      if (_isMounted) {
        // Check mounted state before showing snackbar/setting state
        _safeSetState(() {
          _isLoading = false;
          // Optionally clear lists or set an error flag
          _pastEvents = [];
          _upcomingEvents = [];
          _missedEvents = [];
        });
        _showSnackBar('Error loading events. Please try again.', isError: true);
      }
    }
  }

  // --- Local State Update: Mark as Complete ---
  Future<void> _markEventComplete(EventIsar event) async {
    if (!_isMounted) return;
    // Simple rollback state - store original values
    final originalState = {
      'businessId': event.businessId,
      'isCompleted': event.isCompleted,
      'isMissed': event.isMissed,
      'dueDate': event.dueDate,
      'list': _upcomingEvents.any((e) => e.businessId == event.businessId)
          ? 'upcoming'
          : 'missed',
    };

    EventIsar eventToUpdate = event; // Use the passed event reference

    // Optimistically update UI first
    _safeSetState(() {
      _upcomingEvents.removeWhere((e) => e.businessId == event.businessId);
      _missedEvents.removeWhere((e) => e.businessId == event.businessId);
      if (!_pastEvents.any((e) => e.businessId == event.businessId)) {
        eventToUpdate.isCompleted = true; // Mark as complete on the object
        eventToUpdate.updatedAt = DateTime.now();
        _pastEvents.add(eventToUpdate); // Add the updated object
        _pastEvents.sort(_sortEvents); // Re-sort
      }
    });

    try {
      // Use the updated event object reference
      await _eventService.updateEvent(eventToUpdate);
      _showSnackBar('Event marked as complete.');
    } catch (e, s) {
      debugPrint('Error marking event complete: $e\n$s');
      if (_isMounted) {
        // Rollback UI changes on error
        _safeSetState(() {
          // Find the event we added to past events
          final addedEventIndex = _pastEvents
              .indexWhere((e) => e.businessId == originalState['businessId']);
          if (addedEventIndex != -1) {
            final rolledBackEvent = _pastEvents.removeAt(addedEventIndex);
            // Restore original properties
            rolledBackEvent.isCompleted = originalState['isCompleted'] as bool;
            rolledBackEvent.isMissed = originalState['isMissed'] as bool;
            rolledBackEvent.dueDate = originalState['dueDate'] as DateTime?;
            rolledBackEvent.updatedAt =
                DateTime.now(); // Or store original updatedAt

            // Put it back in the correct original list
            if (originalState['list'] == 'upcoming') {
              _upcomingEvents.add(rolledBackEvent);
              _upcomingEvents.sort(_sortEvents);
            } else {
              _missedEvents.add(rolledBackEvent);
              _missedEvents.sort(_sortEvents);
            }
          }
        });
        _showSnackBar('Failed to mark event complete. Please try again.',
            isError: true);
      }
    }
  }

  // --- Local State Update: Add Event ---
  Future<void> _showAddEventDialog() async {
    if (!_isMounted) return;
    try {
      // Show the dialog and wait for the result (the new event)
      final newEvent = await showDialog<EventIsar>(
        context: context,
        builder: (BuildContext context) {
          return EventFormDialog(
            cattleId: widget.cattle.businessId ?? '',
            // Pass other necessary defaults if needed
          );
        },
      );

      if (!_isMounted) return; // Check again after async gap

      if (newEvent != null) {
        _safeSetState(() {
          // Determine the correct category for the new event
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);
          final dueDate = newEvent.dueDate;
          final isStrictlyPastDue = dueDate != null && dueDate.isBefore(today);

          if (newEvent.isCompleted) {
            _pastEvents.add(newEvent);
            _pastEvents.sort(_sortEvents);
          } else if (newEvent.isMissed || isStrictlyPastDue) {
            _missedEvents.add(newEvent);
            _missedEvents.sort(_sortEvents);
          } else {
            _upcomingEvents.add(newEvent);
            _upcomingEvents.sort(_sortEvents);
          }
        });
        _showSnackBar('Event added successfully.');
      }
    } catch (e, s) {
      debugPrint('Error showing add event dialog or adding event: $e\n$s');
      if (_isMounted) {
        _showSnackBar('Failed to add event. Please try again.', isError: true);
      }
    }
  }

  // --- Local State Update: Edit Event ---
  Future<void> _showEditEventDialog(EventIsar eventToEdit) async {
    if (!_isMounted) return;
    try {
      // Show the dialog and wait for the result (the updated event)
      final updatedEvent = await showDialog<EventIsar>(
        context: context,
        builder: (BuildContext context) {
          return EventFormDialog(
            event: eventToEdit, // Pass the existing event to pre-fill the form
            cattleId: widget.cattle.businessId ?? '',
          );
        },
      );

      if (!_isMounted) return; // Check again after async gap

      if (updatedEvent != null) {
        _safeSetState(() {
          // Remove the original event from all lists (more robust removal)
          _pastEvents
              .removeWhere((e) => e.businessId == eventToEdit.businessId);
          _upcomingEvents
              .removeWhere((e) => e.businessId == eventToEdit.businessId);
          _missedEvents
              .removeWhere((e) => e.businessId == eventToEdit.businessId);

          // Determine the new category for the updated event
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);
          final dueDate = updatedEvent.dueDate;
          final isStrictlyPastDue = dueDate != null && dueDate.isBefore(today);

          if (updatedEvent.isCompleted) {
            _pastEvents.add(updatedEvent);
            _pastEvents.sort(_sortEvents);
          } else if (updatedEvent.isMissed || isStrictlyPastDue) {
            _missedEvents.add(updatedEvent);
            _missedEvents.sort(_sortEvents);
          } else {
            _upcomingEvents.add(updatedEvent);
            _upcomingEvents.sort(_sortEvents);
          }
        });
        _showSnackBar('Event updated successfully.');
      }
    } catch (e, s) {
      debugPrint('Error showing edit event dialog or updating event: $e\n$s');
      if (_isMounted) {
        _showSnackBar('Failed to update event. Please try again.',
            isError: true);
      }
    }
  }

  // --- Delete Functionality ---
  Future<void> _deleteEvent(EventIsar eventToDelete) async {
    if (!_isMounted) return;

    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Deletion'),
          // Use event.title for the message
          content: Text(
              'Are you sure you want to delete the event "${eventToDelete.title ?? 'Untitled Event'}"?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () =>
                  Navigator.of(context).pop(false), // Not confirmed
            ),
            TextButton(
              style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error),
              child: const Text('Delete'),
              onPressed: () => Navigator.of(context).pop(true), // Confirmed
            ),
          ],
        );
      },
    );

    if (!_isMounted) return; // Check again after async gap

    if (confirmed == true) {
      EventIsar? removedEvent;
      int removedIndex = -1;
      String originalList = '';

      // Optimistically remove from UI and store info for rollback
      _safeSetState(() {
        removedIndex = _pastEvents
            .indexWhere((e) => e.businessId == eventToDelete.businessId);
        if (removedIndex != -1) {
          removedEvent = _pastEvents.removeAt(removedIndex);
          originalList = 'past';
        } else {
          removedIndex = _upcomingEvents
              .indexWhere((e) => e.businessId == eventToDelete.businessId);
          if (removedIndex != -1) {
            removedEvent = _upcomingEvents.removeAt(removedIndex);
            originalList = 'upcoming';
          } else {
            removedIndex = _missedEvents
                .indexWhere((e) => e.businessId == eventToDelete.businessId);
            if (removedIndex != -1) {
              removedEvent = _missedEvents.removeAt(removedIndex);
              originalList = 'missed';
            }
          }
        }
      });

      if (removedEvent != null) {
        try {
          // Pass the actual EventIsar object to the service
          await _eventService.deleteEvent(removedEvent!);
          _showSnackBar('Event deleted successfully.');
        } catch (e, s) {
          debugPrint('Error deleting event: $e\n$s');
          if (_isMounted) {
            // Rollback UI changes
            _safeSetState(() {
              if (removedEvent != null && removedIndex != -1) {
                switch (originalList) {
                  case 'past':
                    _pastEvents.insert(removedIndex, removedEvent!);
                    break;
                  case 'upcoming':
                    _upcomingEvents.insert(removedIndex, removedEvent!);
                    break;
                  case 'missed':
                    _missedEvents.insert(removedIndex, removedEvent!);
                    break;
                }
                // No need to re-sort as we inserted at the original position
              }
            });
            _showSnackBar('Failed to delete event. Please try again.',
                isError: true);
          }
        }
      } else {
        debugPrint("Event to delete not found in local lists.");
        // Optionally show a message if the event wasn't found locally
        _showSnackBar('Could not find the event to delete.', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context); // Get theme

    // Handle case where service failed to initialize
    if (!GetIt.instance.isRegistered<IsarService>() ||
        !GetIt.instance<IsarService>().isInitialized) {
      return Scaffold(
        // Return a scaffold to provide structure
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Error: Event service could not be initialized. Please restart the app.',
              style: TextStyle(color: theme.colorScheme.error, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: kPrimaryGreen, // Consistent green color
          unselectedLabelColor: Colors.grey,
          indicatorColor: kPrimaryGreen, // Consistent green color
          tabs: const [
            Tab(
              icon: Icon(Icons.history, color: Colors.blue),
              text: "Past",
            ),
            Tab(
              icon: Icon(Icons.event,
                  color: kPrimaryGreen), // Consistent green color
              text: "Upcoming",
            ),
            Tab(
              icon: Icon(Icons.warning, color: Colors.red),
              text: "Missed",
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Past Events Tab
          _buildEventList(_pastEvents, "Past", Icons.history, "No Past Events",
              "Completed events will appear here"),
          // Upcoming Events Tab
          _buildEventList(_upcomingEvents, "Upcoming", Icons.event,
              "No Upcoming Events", "Future scheduled events will appear here"),
          // Missed Events Tab
          _buildEventList(
              _missedEvents,
              "Missed",
              Icons.warning,
              "No Missed Events",
              "Overdue events will appear here"), // Updated empty text
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddEventDialog,
        tooltip: 'Add New Event',
        backgroundColor: kPrimaryGreen, // Consistent green color
        foregroundColor: Colors.white, // White icon for visibility
        child: const Icon(Icons.add),
      ),
    );
  }

  // Define primary green color as a constant for consistent use
  static const Color kPrimaryGreen = Color(0xFF2E7D32);

  // --- Refactored List Building Logic ---
  Widget _buildEventList(
      List<EventIsar> events,
      String tabName, // For RefreshIndicator semantic label
      IconData emptyIcon,
      String emptyTitle,
      String emptySubtitle) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (events.isEmpty) {
      // Get color based on tab name
      Color iconColor;
      switch (tabName) {
        case "Past":
          iconColor = Colors.blue;
          break;
        case "Upcoming":
          iconColor = kPrimaryGreen;
          break;
        case "Missed":
          iconColor = Colors.red;
          break;
        default:
          iconColor = kPrimaryGreen;
      }

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: iconColor.withAlpha(26), // 0.1 * 255 = 26
                shape: BoxShape.circle,
              ),
              child: Icon(
                emptyIcon,
                size: 48,
                color: iconColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              emptyTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center, // Center align text
            ),
            const SizedBox(height: 8),
            Text(
              emptySubtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87.withAlpha(153), // 0.6 * 255 = 153
              ),
              textAlign: TextAlign.center, // Center align text
            ),
          ],
        ),
      );
    }

    // Use a unique key for each list view based on the tab name
    // Helps Flutter preserve scroll position when switching tabs
    final listViewKey = ValueKey('event_list_$tabName');

    return RefreshIndicator(
      key: ValueKey('refresh_$tabName'), // Key for RefreshIndicator if needed
      semanticsLabel: 'Refresh $tabName Events',
      onRefresh: _loadEvents,
      child: ListView.builder(
        key: listViewKey, // Apply the key here
        padding: const EdgeInsets.all(8),
        itemCount: events.length,
        itemBuilder: (context, index) {
          final event = events[index];
          // Use a unique key for each card, e.g., based on event's businessId
          // Helps with efficient updates when list changes
          final cardKey = ValueKey(event.businessId ?? const Uuid().v4());
          return _buildEventCard(event, cardKey);
        },
      ),
    );
  }

  // Show event details in a dialog
  void _showEventDetails(EventIsar event) {
    final eventColor = event.type?.getColor() ?? Colors.grey;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getIconForEventType(event.type),
              color: eventColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                event.title ?? 'Untitled Event',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow(
                'Type',
                event.type?.getDisplayName() ?? 'Miscellaneous',
                _getIconForEventType(event.type),
                iconColor: eventColor,
              ),
              _buildDetailRow(
                'Date',
                DateFormat('MMM dd, yyyy')
                    .format(event.eventDate ?? DateTime.now()),
                Icons.calendar_today,
                iconColor: Colors.blue,
              ),
              if (event.dueDate != null)
                _buildDetailRow(
                  'Due Date',
                  DateFormat('MMM dd, yyyy').format(event.dueDate!),
                  Icons.event_available,
                  iconColor: kPrimaryGreen, // Consistent green color
                ),
              _buildDetailRow(
                'Status',
                event.isCompleted
                    ? 'Completed'
                    : (event.isMissed ? 'Missed' : 'Pending'),
                event.isCompleted
                    ? Icons.check_circle
                    : (event.isMissed ? Icons.error : Icons.pending),
                iconColor: event.isCompleted
                    ? kPrimaryGreen // Consistent green color
                    : (event.isMissed ? Colors.red : Colors.orange),
              ),
              if (event.notes != null && event.notes!.isNotEmpty)
                _buildDetailRow(
                  'Notes',
                  event.notes!,
                  Icons.notes,
                  iconColor: Colors.grey,
                ),
            ],
          ),
        ),
        actions: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (!event.isCompleted)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: TextButton.icon(
                        onPressed: () {
                          Navigator.pop(dialogContext);
                          _markEventComplete(event);
                        },
                        icon: const Icon(Icons.check_circle,
                            color: kPrimaryGreen), // Consistent green color
                        label: const Text('Complete',
                            style: TextStyle(
                                color:
                                    kPrimaryGreen)), // Consistent green color
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextButton.icon(
                        onPressed: () {
                          Navigator.pop(dialogContext);
                          // Mark as missed
                          _safeSetState(() {
                            event.isMissed = true;
                            event.updatedAt = DateTime.now();
                          });
                          _eventService.updateEvent(event);
                          _loadEvents();
                        },
                        icon: const Icon(Icons.cancel, color: Colors.red),
                        label: const Text('Missed',
                            style: TextStyle(color: Colors.red)),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              Center(
                child: TextButton.icon(
                  onPressed: () => Navigator.pop(dialogContext),
                  icon: const Icon(Icons.close),
                  label: const Text('Close'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build detail rows in the event details dialog
  Widget _buildDetailRow(String label, String value, IconData icon,
      {Color? iconColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: iconColor ?? Colors.grey, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // --- Updated Event Card ---
  Widget _buildEventCard(EventIsar event, Key key) {
    final dateFormat = DateFormat('dd MMM yyyy');
    final dateToShow = event.dueDate ?? event.eventDate;
    // Define today *outside* the check for efficiency if needed elsewhere
    final eventColor = event.type?.getColor() ?? Colors.grey;

    return Card(
      key: key, // Apply the key to the Card
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Icon(
          _getIconForEventType(event.type),
          color: eventColor,
          size: 24,
        ),
        title: Text(
          event.title ?? 'Untitled Event',
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (dateToShow != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  'Date: ${dateFormat.format(dateToShow)}',
                  style: const TextStyle(
                    color: Colors.black87,
                  ),
                ),
              ),
            if (event.dueDate != null)
              Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: Text(
                  'Due: ${dateFormat.format(event.dueDate!)}',
                  style: const TextStyle(
                    color: Colors.black87,
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 2.0),
              child: Text(
                event.isCompleted
                    ? 'Completed'
                    : (event.isMissed ? 'Missed' : 'Pending'),
                style: TextStyle(
                  color: event.isCompleted
                      ? kPrimaryGreen // Consistent green color
                      : (event.isMissed ? Colors.red : Colors.orange),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showEventOptions(event),
        ),
        onTap: () => _showEventDetails(event),
      ),
    );
  }

  // Show event options in a dialog
  void _showEventOptions(EventIsar event) {
    final eventColor = event.type?.getColor() ?? Colors.grey;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getIconForEventType(event.type),
              color: eventColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            const Text('Event Options'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow(
                'Title',
                event.title ?? 'Untitled Event',
                Icons.event,
                iconColor: eventColor,
              ),
              _buildDetailRow(
                'Type',
                event.type?.getDisplayName() ?? 'Miscellaneous',
                _getIconForEventType(event.type),
                iconColor: eventColor,
              ),
              _buildDetailRow(
                'Status',
                event.isCompleted
                    ? 'Completed'
                    : (event.isMissed ? 'Missed' : 'Pending'),
                event.isCompleted
                    ? Icons.check_circle
                    : (event.isMissed ? Icons.error : Icons.pending),
                iconColor: event.isCompleted
                    ? kPrimaryGreen // Consistent green color
                    : (event.isMissed ? Colors.red : Colors.orange),
              ),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton.icon(
                onPressed: () {
                  Navigator.pop(dialogContext);
                  _deleteEvent(event);
                },
                icon: const Icon(Icons.delete, color: Colors.red),
                label:
                    const Text('Delete', style: TextStyle(color: Colors.red)),
              ),
              TextButton.icon(
                onPressed: () => Navigator.pop(dialogContext),
                icon: const Icon(Icons.close),
                label: const Text('Close'),
              ),
              TextButton.icon(
                onPressed: () {
                  Navigator.pop(dialogContext);
                  _showEditEventDialog(event);
                },
                icon: const Icon(Icons.edit),
                label: const Text('Edit'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper to get an icon based on event type (customize as needed)
  // Updated to accept EventTypeEmbedded?
  IconData _getIconForEventType(EventTypeEmbedded? type) {
    // Use the getIcon method from EventTypeEmbedded, provide default
    return type?.getIcon() ?? Icons.event; // Default icon if type is null
  }
}
